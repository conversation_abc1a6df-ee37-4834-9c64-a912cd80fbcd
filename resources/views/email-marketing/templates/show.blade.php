@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-envelope text-primary me-2"></i>
                        {{ $template->name }}
                    </h1>
                    <p class="text-muted mb-0">Chi tiết email template</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.templates') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i> Quay Lại
                    </a>
                    <a href="{{ route('email-marketing.templates.edit', $template) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i> Chỉnh Sửa
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Template Info -->
                <div class="col-md-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-info-circle me-2"></i>Thông Tin Template
                            </h6>
                        </div>
                        <div class="card-body">
                            <p><strong>Tên:</strong> {{ $template->name }}</p>
                            <p><strong>Loại:</strong> 
                                <span class="badge bg-{{ $template->template_type === 'welcome' ? 'success' : ($template->template_type === 'reminder' ? 'warning' : ($template->template_type === 'follow_up' ? 'info' : 'primary')) }}">
                                    @switch($template->template_type)
                                        @case('welcome')
                                            Chào mừng
                                            @break
                                        @case('reminder')
                                            Nhắc nhở
                                            @break
                                        @case('follow_up')
                                            Follow-up
                                            @break
                                        @case('promotional')
                                            Khuyến mãi
                                            @break
                                        @default
                                            Tùy chỉnh
                                    @endswitch
                                </span>
                            </p>
                            <p><strong>Subject:</strong> {{ $template->subject }}</p>
                            @if($template->campaign)
                                <p><strong>Campaign:</strong> 
                                    <a href="{{ route('email-marketing.campaigns.show', $template->campaign) }}">
                                        {{ $template->campaign->name }}
                                    </a>
                                </p>
                            @endif
                            @if($template->is_default)
                                <p><span class="badge bg-success">Template Mặc Định</span></p>
                            @endif
                            <p><strong>Ngày tạo:</strong> {{ $template->created_at->format('d/m/Y H:i') }}</p>
                            <p><strong>Cập nhật:</strong> {{ $template->updated_at->format('d/m/Y H:i') }}</p>
                        </div>
                    </div>

                    <!-- Available Variables -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-success">
                                <i class="fas fa-code me-2"></i>Variables Có Thể Sử Dụng
                            </h6>
                        </div>
                        <div class="card-body">
                            @php
                                $availableVars = $template->getAvailableVariables();
                            @endphp
                            @foreach($availableVars as $var => $description)
                                <div class="mb-2">
                                    <code class="text-primary">{{ $var }}</code>
                                    <br><small class="text-muted">{{ $description }}</small>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Usage Statistics -->
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-info">
                                <i class="fas fa-chart-bar me-2"></i>Thống Kê Sử Dụng
                            </h6>
                        </div>
                        <div class="card-body">
                            <p><strong>Workflows sử dụng:</strong> {{ $template->workflows->count() }}</p>
                            @if($template->workflows->count() > 0)
                                <ul class="list-unstyled">
                                    @foreach($template->workflows as $workflow)
                                        <li class="mb-1">
                                            <a href="{{ route('email-marketing.campaigns.show', $workflow->campaign) }}">
                                                {{ $workflow->campaign->name }}
                                            </a>
                                            <small class="text-muted">(Step {{ $workflow->sequence_order }})</small>
                                        </li>
                                    @endforeach
                                </ul>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Template Preview -->
                <div class="col-md-8">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-eye me-2"></i>Preview Template
                            </h6>
                            <div>
                                <a href="{{ route('email-marketing.templates.preview', $template) }}" 
                                   class="btn btn-sm btn-outline-info" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i> Mở Preview Riêng
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Subject Preview -->
                            <div class="mb-3">
                                <label class="form-label fw-bold">Subject Line:</label>
                                <div class="p-2 bg-light border rounded">
                                    {{ $template->subject }}
                                </div>
                            </div>

                            <!-- Content Preview -->
                            <div class="mb-3">
                                <label class="form-label fw-bold">Email Content:</label>
                                <div class="border rounded" style="max-height: 600px; overflow-y: auto;">
                                    <iframe srcdoc="{{ htmlspecialchars($template->content) }}" 
                                            style="width: 100%; height: 500px; border: none;">
                                    </iframe>
                                </div>
                            </div>

                            <!-- Raw HTML -->
                            <div class="mb-3">
                                <label class="form-label fw-bold">HTML Source:</label>
                                <div class="collapse" id="htmlSource">
                                    <textarea class="form-control" rows="10" readonly>{{ $template->content }}</textarea>
                                </div>
                                <button class="btn btn-sm btn-outline-secondary" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#htmlSource">
                                    <i class="fas fa-code me-1"></i> Xem HTML Source
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between">
                        <div>
                            <form action="{{ route('email-marketing.templates.duplicate', $template) }}" 
                                  method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-copy me-1"></i> Sao Chép Template
                                </button>
                            </form>
                            <a href="{{ route('email-marketing.templates.preview', $template) }}" 
                               class="btn btn-info ms-2" target="_blank">
                                <i class="fas fa-eye me-1"></i> Preview Với Sample Data
                            </a>
                        </div>
                        <div>
                            @if(!$template->is_default && $template->workflows->count() === 0)
                                <form action="{{ route('email-marketing.templates.destroy', $template) }}" 
                                      method="POST" class="d-inline" 
                                      onsubmit="return confirm('Bạn có chắc chắn muốn xóa template này? Hành động này không thể hoàn tác.')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-trash me-1"></i> Xóa Template
                                    </button>
                                </form>
                            @elseif($template->workflows->count() > 0)
                                <button class="btn btn-danger" disabled title="Không thể xóa template đang được sử dụng">
                                    <i class="fas fa-trash me-1"></i> Không Thể Xóa
                                </button>
                            @else
                                <button class="btn btn-danger" disabled title="Không thể xóa template mặc định">
                                    <i class="fas fa-trash me-1"></i> Template Mặc Định
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.card-header h6 {
    color: #5a5c69;
}

code {
    background-color: #f8f9fc;
    padding: 2px 4px;
    border-radius: 3px;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
</style>
@endpush
