@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-envelope text-primary me-2"></i>
                        Email Templates
                    </h1>
                    <p class="text-muted mb-0">Quản lý mẫu email marketing</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i> Quay Lại Dashboard
                    </a>
                    <a href="{{ route('email-marketing.templates.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Tạo Template Mới
                    </a>
                </div>
            </div>

            <!-- Templates List -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>Danh Sách Templates
                    </h6>
                </div>
                <div class="card-body">
                    @if($templates->count() > 0)
                        <div class="row">
                            @foreach($templates as $template)
                                <div class="col-lg-6 col-xl-4 mb-4">
                                    <div class="card h-100 border-left-{{ $template->template_type === 'welcome' ? 'success' : ($template->template_type === 'reminder' ? 'warning' : ($template->template_type === 'follow_up' ? 'info' : 'primary')) }}">
                                        <div class="card-header py-2">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="m-0 font-weight-bold">{{ $template->name }}</h6>
                                                @if($template->is_default)
                                                    <span class="badge bg-success">Default</span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <p class="text-muted mb-2">
                                                <strong>Subject:</strong> {{ Str::limit($template->subject, 50) }}
                                            </p>
                                            <p class="mb-2">
                                                <span class="badge bg-{{ $template->template_type === 'welcome' ? 'success' : ($template->template_type === 'reminder' ? 'warning' : ($template->template_type === 'follow_up' ? 'info' : 'primary')) }}">
                                                    @switch($template->template_type)
                                                        @case('welcome')
                                                            Chào mừng
                                                            @break
                                                        @case('reminder')
                                                            Nhắc nhở
                                                            @break
                                                        @case('follow_up')
                                                            Follow-up
                                                            @break
                                                        @case('promotional')
                                                            Khuyến mãi
                                                            @break
                                                        @default
                                                            Tùy chỉnh
                                                    @endswitch
                                                </span>
                                            </p>
                                            @if($template->campaign)
                                                <p class="mb-2">
                                                    <small class="text-muted">
                                                        <i class="fas fa-bullhorn me-1"></i>{{ $template->campaign->name }}
                                                    </small>
                                                </p>
                                            @endif
                                            <p class="text-muted mb-3">
                                                <small>Tạo: {{ $template->created_at->format('d/m/Y') }}</small>
                                            </p>
                                        </div>
                                        <div class="card-footer">
                                            <div class="btn-group w-100" role="group">
                                                <a href="{{ route('email-marketing.templates.preview', $template) }}" 
                                                   class="btn btn-sm btn-outline-info" title="Preview">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('email-marketing.templates.show', $template) }}" 
                                                   class="btn btn-sm btn-outline-primary" title="Chi tiết">
                                                    <i class="fas fa-info-circle"></i>
                                                </a>
                                                <a href="{{ route('email-marketing.templates.edit', $template) }}" 
                                                   class="btn btn-sm btn-outline-warning" title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('email-marketing.templates.duplicate', $template) }}" 
                                                      method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-secondary" title="Sao chép">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </form>
                                                @if(!$template->is_default)
                                                    <form action="{{ route('email-marketing.templates.destroy', $template) }}" 
                                                          method="POST" class="d-inline" 
                                                          onsubmit="return confirm('Bạn có chắc chắn muốn xóa template này?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Xóa">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            {{ $templates->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-envelope fa-4x text-gray-300 mb-4"></i>
                            <h4 class="text-gray-600">Chưa có template nào</h4>
                            <p class="text-muted mb-4">Tạo template đầu tiên hoặc sử dụng templates có sẵn</p>
                            <a href="{{ route('email-marketing.templates.create') }}" class="btn btn-primary me-2">
                                <i class="fas fa-plus me-2"></i>Tạo Template Mới
                            </a>
                            <button class="btn btn-success" onclick="loadDefaultTemplates()">
                                <i class="fas fa-download me-2"></i>Tải Templates Mặc Định
                            </button>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Template Types Info -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle me-2"></i>Các Loại Template
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="border-left-success p-3">
                                <h6 class="text-success">Welcome Email</h6>
                                <p class="text-muted mb-0">Gửi ngay khi có người đăng ký webinar</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="border-left-warning p-3">
                                <h6 class="text-warning">Reminder Email</h6>
                                <p class="text-muted mb-0">Nhắc nhở trước khi webinar diễn ra</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="border-left-info p-3">
                                <h6 class="text-info">Follow-up Email</h6>
                                <p class="text-muted mb-0">Cảm ơn và gửi materials sau webinar</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="border-left-primary p-3">
                                <h6 class="text-primary">Promotional Email</h6>
                                <p class="text-muted mb-0">Khuyến mãi và ưu đãi đặc biệt</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.btn-group .btn {
    border-radius: 0.25rem !important;
    margin-right: 2px;
}
.card-footer {
    background-color: #f8f9fc;
}
</style>
@endpush

@push('scripts')
<script>
function loadDefaultTemplates() {
    if (confirm('Bạn có muốn tải các template mặc định? Điều này sẽ tạo 4 templates cơ bản.')) {
        // Redirect to seeder or create default templates
        alert('Tính năng này sẽ được phát triển. Hiện tại bạn có thể chạy: php artisan db:seed --class=EmailTemplateSeeder');
    }
}
</script>
@endpush
