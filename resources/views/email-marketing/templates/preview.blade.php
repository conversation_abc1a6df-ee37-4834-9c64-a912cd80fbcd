<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview: {{ $template->name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .preview-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .preview-header {
            background: linear-gradient(45deg, #4e73df, #224abe);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .preview-info {
            background: #f8f9fc;
            padding: 15px 20px;
            border-bottom: 1px solid #e3e6f0;
        }
        .email-content {
            padding: 0;
        }
        .preview-footer {
            background: #f8f9fc;
            padding: 15px 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .info-item {
            margin-bottom: 8px;
        }
        .info-label {
            font-weight: bold;
            color: #5a5c69;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .badge-success { background-color: #1cc88a; }
        .badge-warning { background-color: #f6c23e; }
        .badge-info { background-color: #36b9cc; }
        .badge-primary { background-color: #4e73df; }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            margin: 5px;
            text-decoration: none;
            border-radius: 5px;
            font-size: 14px;
            border: none;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #4e73df;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <!-- Preview Header -->
        <div class="preview-header">
            <h1>📧 Email Template Preview</h1>
            <p>{{ $template->name }}</p>
        </div>

        <!-- Template Info -->
        <div class="preview-info">
            <div class="info-item">
                <span class="info-label">Template:</span> {{ $template->name }}
            </div>
            <div class="info-item">
                <span class="info-label">Type:</span> 
                <span class="badge badge-{{ $template->template_type === 'welcome' ? 'success' : ($template->template_type === 'reminder' ? 'warning' : ($template->template_type === 'follow_up' ? 'info' : 'primary')) }}">
                    @switch($template->template_type)
                        @case('welcome')
                            Chào mừng
                            @break
                        @case('reminder')
                            Nhắc nhở
                            @break
                        @case('follow_up')
                            Follow-up
                            @break
                        @case('promotional')
                            Khuyến mãi
                            @break
                        @default
                            Tùy chỉnh
                    @endswitch
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">Subject:</span> {{ $rendered['subject'] }}
            </div>
            <div class="info-item">
                <span class="info-label">Preview với sample data:</span> 
                <small class="text-muted">Dữ liệu mẫu được sử dụng để hiển thị</small>
            </div>
        </div>

        <!-- Email Content -->
        <div class="email-content">
            {!! $rendered['content'] !!}
        </div>

        <!-- Preview Footer -->
        <div class="preview-footer">
            <p><strong>🔍 Đây là preview với dữ liệu mẫu</strong></p>
            <p>Email thực tế sẽ sử dụng dữ liệu từ webinar và participant</p>
            
            <div style="margin-top: 15px;">
                <a href="{{ route('email-marketing.templates.show', $template) }}" class="btn btn-primary">
                    ← Quay Lại Template
                </a>
                <a href="{{ route('email-marketing.templates.edit', $template) }}" class="btn btn-secondary">
                    ✏️ Chỉnh Sửa
                </a>
                <button onclick="window.print()" class="btn btn-secondary">
                    🖨️ In Preview
                </button>
            </div>
        </div>
    </div>

    <script>
        // Auto-resize iframe if any
        document.addEventListener('DOMContentLoaded', function() {
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach(function(iframe) {
                iframe.onload = function() {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const height = iframeDoc.body.scrollHeight;
                        iframe.style.height = height + 'px';
                    } catch (e) {
                        console.log('Cannot access iframe content');
                    }
                };
            });
        });

        // Print styles
        window.addEventListener('beforeprint', function() {
            document.querySelector('.preview-footer').style.display = 'none';
        });

        window.addEventListener('afterprint', function() {
            document.querySelector('.preview-footer').style.display = 'block';
        });
    </script>
</body>
</html>
