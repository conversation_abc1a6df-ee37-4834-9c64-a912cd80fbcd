@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-plus text-primary me-2"></i>
                        Tạo Email Template
                    </h1>
                    <p class="text-muted mb-0">Tạo mẫu email marketing mới</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.templates') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Quay Lại
                    </a>
                </div>
            </div>

            <!-- Create Form -->
            <form action="{{ route('email-marketing.templates.store') }}" method="POST">
                @csrf
                
                <div class="row">
                    <!-- Template Info -->
                    <div class="col-md-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-info-circle me-2"></i>Thông Tin Template
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- Template Name -->
                                <div class="mb-3">
                                    <label for="name" class="form-label">
                                        Tên Template <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" 
                                           placeholder="Nhập tên template" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Template Type -->
                                <div class="mb-3">
                                    <label for="template_type" class="form-label">
                                        Loại Template <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select @error('template_type') is-invalid @enderror" 
                                            id="template_type" name="template_type" required>
                                        <option value="">Chọn loại template</option>
                                        <option value="welcome" {{ old('template_type') == 'welcome' ? 'selected' : '' }}>
                                            Welcome - Chào mừng
                                        </option>
                                        <option value="reminder" {{ old('template_type') == 'reminder' ? 'selected' : '' }}>
                                            Reminder - Nhắc nhở
                                        </option>
                                        <option value="follow_up" {{ old('template_type') == 'follow_up' ? 'selected' : '' }}>
                                            Follow-up - Theo dõi
                                        </option>
                                        <option value="promotional" {{ old('template_type') == 'promotional' ? 'selected' : '' }}>
                                            Promotional - Khuyến mãi
                                        </option>
                                        <option value="custom" {{ old('template_type') == 'custom' ? 'selected' : '' }}>
                                            Custom - Tùy chỉnh
                                        </option>
                                    </select>
                                    @error('template_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Campaign -->
                                <div class="mb-3">
                                    <label for="campaign_id" class="form-label">Campaign (Tùy chọn)</label>
                                    <select class="form-select @error('campaign_id') is-invalid @enderror" 
                                            id="campaign_id" name="campaign_id">
                                        <option value="">Không liên kết campaign</option>
                                        @foreach($campaigns as $campaign)
                                            <option value="{{ $campaign->id }}" {{ old('campaign_id') == $campaign->id ? 'selected' : '' }}>
                                                {{ $campaign->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('campaign_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Available Variables -->
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-success">
                                    <i class="fas fa-code me-2"></i>Variables Có Thể Sử Dụng
                                </h6>
                            </div>
                            <div class="card-body" id="variablesList">
                                <div class="mb-2">
                                    <code>{{site_name}}</code><br>
                                    <small class="text-muted">Tên website</small>
                                </div>
                                <div class="mb-2">
                                    <code>{{participant_name}}</code><br>
                                    <small class="text-muted">Tên người tham gia</small>
                                </div>
                                <div class="mb-2">
                                    <code>{{webinar_title}}</code><br>
                                    <small class="text-muted">Tiêu đề webinar</small>
                                </div>
                                <div class="mb-2">
                                    <code>{{join_url}}</code><br>
                                    <small class="text-muted">Link tham gia</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Content -->
                    <div class="col-md-8">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-envelope me-2"></i>Nội Dung Email
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- Subject -->
                                <div class="mb-3">
                                    <label for="subject" class="form-label">
                                        Tiêu Đề Email <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control @error('subject') is-invalid @enderror" 
                                           id="subject" name="subject" value="{{ old('subject') }}" 
                                           placeholder="Nhập tiêu đề email" required>
                                    @error('subject')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Content -->
                                <div class="mb-3">
                                    <label for="content" class="form-label">
                                        Nội Dung Email <span class="text-danger">*</span>
                                    </label>
                                    <textarea class="form-control @error('content') is-invalid @enderror" 
                                              id="content" name="content" rows="15" required>{{ old('content', '<p>Xin chào {{participant_name}},</p><p>Cảm ơn bạn đã đăng ký tham gia <strong>{{webinar_title}}</strong>.</p><p><a href="{{join_url}}">Tham gia webinar</a></p><p>Trân trọng,<br>{{site_name}}</p>') }}</textarea>
                                    @error('content')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        Hỗ trợ HTML. Sử dụng variables để personalize email.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('email-marketing.templates') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i> Hủy
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Tạo Template
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
code {
    background-color: #f8f9fc;
    padding: 2px 4px;
    border-radius: 3px;
    color: #e83e8c;
}
</style>
@endpush
