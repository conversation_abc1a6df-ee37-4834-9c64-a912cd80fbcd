@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-plus text-primary me-2"></i>
                        Tạo Campaign Email Marketing
                    </h1>
                    <p class="text-muted mb-0">Tạo chiến dịch email marketing mới</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.campaigns') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Quay Lại
                    </a>
                </div>
            </div>

            <!-- Create Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Thông Tin Campaign
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('email-marketing.campaigns.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <!-- Campaign Name -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">
                                    Tên Campaign <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" 
                                       placeholder="Nhập tên campaign" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Webinar Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="webinar_id" class="form-label">Webinar</label>
                                <select class="form-select @error('webinar_id') is-invalid @enderror" 
                                        id="webinar_id" name="webinar_id">
                                    <option value="">Tất cả webinars</option>
                                    @foreach($webinars as $webinar)
                                        <option value="{{ $webinar->id }}" {{ old('webinar_id') == $webinar->id ? 'selected' : '' }}>
                                            {{ $webinar->title }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('webinar_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">
                                    Để trống để áp dụng cho tất cả webinars
                                </small>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">Mô Tả</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Mô tả ngắn về campaign này">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Trigger Type -->
                        <div class="mb-4">
                            <label for="trigger_type" class="form-label">
                                Loại Kích Hoạt <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('trigger_type') is-invalid @enderror" 
                                    id="trigger_type" name="trigger_type" required>
                                <option value="">Chọn loại kích hoạt</option>
                                <option value="registration" {{ old('trigger_type') == 'registration' ? 'selected' : '' }}>
                                    Khi đăng ký webinar
                                </option>
                                <option value="before_webinar" {{ old('trigger_type') == 'before_webinar' ? 'selected' : '' }}>
                                    Trước khi webinar diễn ra
                                </option>
                                <option value="after_webinar" {{ old('trigger_type') == 'after_webinar' ? 'selected' : '' }}>
                                    Sau khi webinar kết thúc
                                </option>
                                <option value="custom" {{ old('trigger_type') == 'custom' ? 'selected' : '' }}>
                                    Tùy chỉnh
                                </option>
                            </select>
                            @error('trigger_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Trigger Type Descriptions -->
                        <div class="alert alert-info mb-4">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>Giải thích các loại kích hoạt:
                            </h6>
                            <ul class="mb-0">
                                <li><strong>Khi đăng ký webinar:</strong> Gửi email ngay khi có người đăng ký tham gia webinar</li>
                                <li><strong>Trước khi webinar diễn ra:</strong> Gửi email nhắc nhở trước khi webinar bắt đầu</li>
                                <li><strong>Sau khi webinar kết thúc:</strong> Gửi email follow-up sau khi webinar hoàn thành</li>
                                <li><strong>Tùy chỉnh:</strong> Thiết lập điều kiện kích hoạt riêng</li>
                            </ul>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('email-marketing.campaigns') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i> Hủy
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Tạo Campaign
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Next Steps Info -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-lightbulb me-2"></i>Bước Tiếp Theo
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-3">Sau khi tạo campaign, bạn cần:</p>
                    <ol>
                        <li class="mb-2">
                            <strong>Tạo Email Templates:</strong> Thiết kế nội dung email sẽ được gửi
                        </li>
                        <li class="mb-2">
                            <strong>Thiết lập Workflow:</strong> Xác định thứ tự và thời gian gửi email
                        </li>
                        <li class="mb-2">
                            <strong>Kích hoạt Campaign:</strong> Bật campaign để bắt đầu gửi email tự động
                        </li>
                        <li class="mb-0">
                            <strong>Theo dõi hiệu quả:</strong> Xem thống kê mở email, click rate, etc.
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.alert-info {
    background-color: #e7f3ff;
    border-color: #b8daff;
    color: #004085;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
}

.card-header h6 {
    color: #5a5c69;
}

.btn-primary {
    background: linear-gradient(45deg, #4e73df, #224abe);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #224abe, #1e3a8a);
}
</style>
@endpush
