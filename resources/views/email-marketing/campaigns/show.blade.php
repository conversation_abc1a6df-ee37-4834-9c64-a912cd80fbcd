@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-bullhorn text-primary me-2"></i>
                        {{ $campaign->name }}
                    </h1>
                    <p class="text-muted mb-0">Chi tiết campaign email marketing</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.campaigns') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i> Quay Lại
                    </a>
                    <a href="{{ route('email-marketing.campaigns.edit', $campaign) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i> Chỉnh Sửa
                    </a>
                </div>
            </div>

            <!-- Campaign Info -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-info-circle me-2"></i>Thông Tin Campaign
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Tên Campaign:</strong> {{ $campaign->name }}</p>
                                    <p><strong>Webinar:</strong> 
                                        @if($campaign->webinar)
                                            <span class="badge bg-info">{{ $campaign->webinar->title }}</span>
                                        @else
                                            <span class="text-muted">Tất cả webinars</span>
                                        @endif
                                    </p>
                                    <p><strong>Loại Trigger:</strong> 
                                        <span class="badge bg-secondary">
                                            @switch($campaign->trigger_type)
                                                @case('registration')
                                                    Đăng ký
                                                    @break
                                                @case('before_webinar')
                                                    Trước webinar
                                                    @break
                                                @case('after_webinar')
                                                    Sau webinar
                                                    @break
                                                @default
                                                    Tùy chỉnh
                                            @endswitch
                                        </span>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Trạng Thái:</strong> 
                                        <span class="badge bg-{{ $campaign->status === 'active' ? 'success' : ($campaign->status === 'draft' ? 'secondary' : 'warning') }}">
                                            @switch($campaign->status)
                                                @case('active')
                                                    Đang chạy
                                                    @break
                                                @case('draft')
                                                    Nháp
                                                    @break
                                                @case('paused')
                                                    Tạm dừng
                                                    @break
                                                @default
                                                    Hoàn thành
                                            @endswitch
                                        </span>
                                    </p>
                                    <p><strong>Ngày Tạo:</strong> {{ $campaign->created_at->format('d/m/Y H:i') }}</p>
                                    @if($campaign->started_at)
                                        <p><strong>Ngày Bắt Đầu:</strong> {{ $campaign->started_at->format('d/m/Y H:i') }}</p>
                                    @endif
                                </div>
                            </div>
                            @if($campaign->description)
                                <div class="mt-3">
                                    <strong>Mô Tả:</strong>
                                    <p class="text-muted">{{ $campaign->description }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="col-md-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-success">
                                <i class="fas fa-chart-bar me-2"></i>Thống Kê
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted">Emails Đã Gửi</small>
                                <h4 class="text-primary">{{ number_format($stats['total_sent']) }}</h4>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Tỷ Lệ Mở</small>
                                <h4 class="text-success">{{ $stats['open_rate'] }}%</h4>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" style="width: {{ $stats['open_rate'] }}%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Tỷ Lệ Click</small>
                                <h4 class="text-info">{{ $stats['click_rate'] }}%</h4>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-info" style="width: {{ $stats['click_rate'] }}%"></div>
                                </div>
                            </div>
                            @if($stats['total_failed'] > 0)
                                <div class="mb-3">
                                    <small class="text-muted">Emails Thất Bại</small>
                                    <h4 class="text-danger">{{ number_format($stats['total_failed']) }}</h4>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Workflows -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-sitemap me-2"></i>Email Workflows
                    </h6>
                    <button class="btn btn-sm btn-success" onclick="alert('Tính năng này sẽ được phát triển trong phiên bản tiếp theo')">
                        <i class="fas fa-plus me-1"></i> Thêm Workflow
                    </button>
                </div>
                <div class="card-body">
                    @if($campaign->workflows->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>Thứ Tự</th>
                                        <th>Template</th>
                                        <th>Delay</th>
                                        <th>Trạng Thái</th>
                                        <th>Emails Gửi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($campaign->workflows as $workflow)
                                        <tr>
                                            <td>{{ $workflow->sequence_order }}</td>
                                            <td>
                                                @if($workflow->emailTemplate)
                                                    {{ $workflow->emailTemplate->name }}
                                                    <br><small class="text-muted">{{ $workflow->emailTemplate->subject }}</small>
                                                @else
                                                    <span class="text-danger">Template đã bị xóa</span>
                                                @endif
                                            </td>
                                            <td>{{ $workflow->getDelayText() }}</td>
                                            <td>
                                                <span class="badge bg-{{ $workflow->is_active ? 'success' : 'secondary' }}">
                                                    {{ $workflow->is_active ? 'Hoạt động' : 'Tạm dừng' }}
                                                </span>
                                            </td>
                                            <td>{{ number_format($workflow->emailLogs->count()) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-sitemap fa-3x text-gray-300 mb-3"></i>
                            <h5 class="text-gray-600">Chưa có workflow nào</h5>
                            <p class="text-muted">Thêm workflow để thiết lập chuỗi email tự động</p>
                            <button class="btn btn-primary" onclick="alert('Tính năng này sẽ được phát triển trong phiên bản tiếp theo')">
                                <i class="fas fa-plus me-2"></i>Tạo Workflow Đầu Tiên
                            </button>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Recent Email Logs -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-envelope me-2"></i>Email Logs Gần Đây
                    </h6>
                </div>
                <div class="card-body">
                    @if($campaign->emailLogs->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>Người Nhận</th>
                                        <th>Tiêu Đề</th>
                                        <th>Trạng Thái</th>
                                        <th>Thời Gian Gửi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($campaign->emailLogs->take(10) as $log)
                                        <tr>
                                            <td>
                                                {{ $log->recipient_name }}<br>
                                                <small class="text-muted">{{ $log->recipient_email }}</small>
                                            </td>
                                            <td>{{ $log->subject }}</td>
                                            <td>
                                                <span class="badge bg-{{ $log->getStatusBadgeColor() }}">
                                                    {{ $log->getStatusText() }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($log->sent_at)
                                                    {{ $log->sent_at->format('d/m/Y H:i') }}
                                                @elseif($log->scheduled_at)
                                                    <small class="text-muted">Lên lịch: {{ $log->scheduled_at->format('d/m/Y H:i') }}</small>
                                                @else
                                                    <small class="text-muted">Chưa lên lịch</small>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @if($campaign->emailLogs->count() > 10)
                            <div class="text-center mt-3">
                                <button class="btn btn-outline-primary" onclick="alert('Tính năng xem tất cả logs sẽ được phát triển')">
                                    Xem Tất Cả Logs
                                </button>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-envelope fa-3x text-gray-300 mb-3"></i>
                            <h5 class="text-gray-600">Chưa có email nào được gửi</h5>
                            <p class="text-muted">Emails sẽ được gửi tự động khi có trigger events</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between">
                        <div>
                            @if($campaign->status === 'draft')
                                <form action="{{ route('email-marketing.campaigns.toggle-status', $campaign) }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-play me-1"></i> Kích Hoạt Campaign
                                    </button>
                                </form>
                            @elseif($campaign->status === 'active')
                                <form action="{{ route('email-marketing.campaigns.toggle-status', $campaign) }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-pause me-1"></i> Tạm Dừng Campaign
                                    </button>
                                </form>
                            @elseif($campaign->status === 'paused')
                                <form action="{{ route('email-marketing.campaigns.toggle-status', $campaign) }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-play me-1"></i> Tiếp Tục Campaign
                                    </button>
                                </form>
                            @endif
                        </div>
                        <div>
                            <form action="{{ route('email-marketing.campaigns.destroy', $campaign) }}" method="POST" class="d-inline" onsubmit="return confirm('Bạn có chắc chắn muốn xóa campaign này? Hành động này không thể hoàn tác.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash me-1"></i> Xóa Campaign
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.progress {
    background-color: #e9ecef;
}
.card-header h6 {
    color: #5a5c69;
}
</style>
@endpush
