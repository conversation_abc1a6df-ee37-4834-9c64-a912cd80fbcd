@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-bullhorn text-primary me-2"></i>
                        Email Campaigns
                    </h1>
                    <p class="text-muted mb-0">Quản lý các chiến dịch email marketing</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i> Quay Lại Dashboard
                    </a>
                    <a href="{{ route('email-marketing.campaigns.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Tạo Campaign Mới
                    </a>
                </div>
            </div>

            <!-- Campaigns List -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>Danh Sách Campaigns
                    </h6>
                </div>
                <div class="card-body">
                    @if($campaigns->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Tên Campaign</th>
                                        <th>Webinar</th>
                                        <th>Loại Trigger</th>
                                        <th>Trạng Thái</th>
                                        <th>Emails Gửi</th>
                                        <th>Tỷ Lệ Mở</th>
                                        <th>Ngày Tạo</th>
                                        <th>Thao Tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($campaigns as $campaign)
                                        @php
                                            $stats = $campaign->stats;
                                        @endphp
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div>
                                                        <h6 class="mb-0">
                                                            <a href="{{ route('email-marketing.campaigns.show', $campaign) }}" class="text-decoration-none">
                                                                {{ $campaign->name }}
                                                            </a>
                                                        </h6>
                                                        @if($campaign->description)
                                                            <small class="text-muted">{{ Str::limit($campaign->description, 50) }}</small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if($campaign->webinar)
                                                    <span class="badge bg-info">{{ $campaign->webinar->title }}</span>
                                                @else
                                                    <span class="text-muted">Tất cả webinars</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    @switch($campaign->trigger_type)
                                                        @case('registration')
                                                            Đăng ký
                                                            @break
                                                        @case('before_webinar')
                                                            Trước webinar
                                                            @break
                                                        @case('after_webinar')
                                                            Sau webinar
                                                            @break
                                                        @default
                                                            Tùy chỉnh
                                                    @endswitch
                                                </span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-{{ $campaign->status === 'active' ? 'success' : ($campaign->status === 'draft' ? 'secondary' : 'warning') }}">
                                                        @switch($campaign->status)
                                                            @case('active')
                                                                Đang chạy
                                                                @break
                                                            @case('draft')
                                                                Nháp
                                                                @break
                                                            @case('paused')
                                                                Tạm dừng
                                                                @break
                                                            @default
                                                                Hoàn thành
                                                        @endswitch
                                                    </span>
                                                    @if($campaign->status !== 'draft')
                                                        <form action="{{ route('email-marketing.campaigns.toggle-status', $campaign) }}" method="POST" class="ms-2">
                                                            @csrf
                                                            <button type="submit" class="btn btn-sm btn-outline-{{ $campaign->status === 'active' ? 'warning' : 'success' }}" title="{{ $campaign->status === 'active' ? 'Tạm dừng' : 'Kích hoạt' }}">
                                                                <i class="fas fa-{{ $campaign->status === 'active' ? 'pause' : 'play' }}"></i>
                                                            </button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-bold">{{ number_format($stats['total_sent']) }}</span>
                                                @if($stats['total_failed'] > 0)
                                                    <br><small class="text-danger">{{ $stats['total_failed'] }} thất bại</small>
                                                @endif
                                            </td>
                                            <td>
                                                @if($stats['total_sent'] > 0)
                                                    <div class="d-flex align-items-center">
                                                        <span class="me-2">{{ $stats['open_rate'] }}%</span>
                                                        <div class="progress" style="width: 60px; height: 8px;">
                                                            <div class="progress-bar bg-success" style="width: {{ $stats['open_rate'] }}%"></div>
                                                        </div>
                                                    </div>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <small>{{ $campaign->created_at->format('d/m/Y H:i') }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('email-marketing.campaigns.show', $campaign) }}" class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('email-marketing.campaigns.edit', $campaign) }}" class="btn btn-sm btn-outline-primary" title="Chỉnh sửa">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('email-marketing.campaigns.destroy', $campaign) }}" method="POST" class="d-inline" onsubmit="return confirm('Bạn có chắc chắn muốn xóa campaign này?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Xóa">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            {{ $campaigns->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-bullhorn fa-4x text-gray-300 mb-4"></i>
                            <h4 class="text-gray-600">Chưa có campaign nào</h4>
                            <p class="text-muted mb-4">Tạo campaign đầu tiên để bắt đầu email marketing</p>
                            <a href="{{ route('email-marketing.campaigns.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Tạo Campaign Đầu Tiên
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.progress {
    background-color: #e9ecef;
}
.btn-group .btn {
    border-radius: 0.25rem !important;
    margin-right: 2px;
}
.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
}
</style>
@endpush
