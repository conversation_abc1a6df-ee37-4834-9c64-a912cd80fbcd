@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-edit text-primary me-2"></i>
                        Chỉnh Sửa Campaign
                    </h1>
                    <p class="text-muted mb-0">Cập nhật thông tin campaign: {{ $campaign->name }}</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.campaigns.show', $campaign) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Quay Lại
                    </a>
                </div>
            </div>

            <!-- Edit Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Thông Tin Campaign
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('email-marketing.campaigns.update', $campaign) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <!-- Campaign Name -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">
                                    Tên Campaign <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $campaign->name) }}" 
                                       placeholder="Nhập tên campaign" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Webinar Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="webinar_id" class="form-label">Webinar</label>
                                <select class="form-select @error('webinar_id') is-invalid @enderror" 
                                        id="webinar_id" name="webinar_id">
                                    <option value="">Tất cả webinars</option>
                                    @foreach($webinars as $webinar)
                                        <option value="{{ $webinar->id }}" 
                                                {{ old('webinar_id', $campaign->webinar_id) == $webinar->id ? 'selected' : '' }}>
                                            {{ $webinar->title }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('webinar_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">
                                    Để trống để áp dụng cho tất cả webinars
                                </small>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">Mô Tả</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Mô tả ngắn về campaign này">{{ old('description', $campaign->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Trigger Type -->
                        <div class="mb-4">
                            <label for="trigger_type" class="form-label">
                                Loại Kích Hoạt <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('trigger_type') is-invalid @enderror" 
                                    id="trigger_type" name="trigger_type" required>
                                <option value="">Chọn loại kích hoạt</option>
                                <option value="registration" {{ old('trigger_type', $campaign->trigger_type) == 'registration' ? 'selected' : '' }}>
                                    Khi đăng ký webinar
                                </option>
                                <option value="before_webinar" {{ old('trigger_type', $campaign->trigger_type) == 'before_webinar' ? 'selected' : '' }}>
                                    Trước khi webinar diễn ra
                                </option>
                                <option value="after_webinar" {{ old('trigger_type', $campaign->trigger_type) == 'after_webinar' ? 'selected' : '' }}>
                                    Sau khi webinar kết thúc
                                </option>
                                <option value="custom" {{ old('trigger_type', $campaign->trigger_type) == 'custom' ? 'selected' : '' }}>
                                    Tùy chỉnh
                                </option>
                            </select>
                            @error('trigger_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Current Status Info -->
                        <div class="alert alert-info mb-4">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>Trạng thái hiện tại:
                            </h6>
                            <p class="mb-2">
                                <strong>Status:</strong> 
                                <span class="badge bg-{{ $campaign->status === 'active' ? 'success' : ($campaign->status === 'draft' ? 'secondary' : 'warning') }}">
                                    @switch($campaign->status)
                                        @case('active')
                                            Đang chạy
                                            @break
                                        @case('draft')
                                            Nháp
                                            @break
                                        @case('paused')
                                            Tạm dừng
                                            @break
                                        @default
                                            Hoàn thành
                                    @endswitch
                                </span>
                            </p>
                            @if($campaign->started_at)
                                <p class="mb-0">
                                    <strong>Ngày bắt đầu:</strong> {{ $campaign->started_at->format('d/m/Y H:i') }}
                                </p>
                            @endif
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('email-marketing.campaigns.show', $campaign) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i> Hủy
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Cập Nhật Campaign
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Warning for Active Campaigns -->
            @if($campaign->status === 'active')
                <div class="card shadow mt-4">
                    <div class="card-header py-3 bg-warning">
                        <h6 class="m-0 font-weight-bold text-dark">
                            <i class="fas fa-exclamation-triangle me-2"></i>Lưu Ý Quan Trọng
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">Campaign này đang ở trạng thái <strong>ACTIVE</strong>. Khi bạn thay đổi thông tin:</p>
                        <ul>
                            <li>Các email đã được lên lịch sẽ không bị ảnh hưởng</li>
                            <li>Chỉ các trigger events mới sẽ sử dụng cấu hình mới</li>
                            <li>Nếu thay đổi trigger type, có thể ảnh hưởng đến workflow</li>
                        </ul>
                        <p class="mb-0 text-warning">
                            <strong>Khuyến nghị:</strong> Tạm dừng campaign trước khi thay đổi cấu hình quan trọng.
                        </p>
                    </div>
                </div>
            @endif

            <!-- Statistics Summary -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-chart-bar me-2"></i>Thống Kê Hiện Tại
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <h4 class="text-primary">{{ $campaign->workflows->count() }}</h4>
                            <small class="text-muted">Workflows</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-info">{{ number_format($campaign->emailLogs->count()) }}</h4>
                            <small class="text-muted">Emails Đã Gửi</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-success">{{ $campaign->stats['open_rate'] }}%</h4>
                            <small class="text-muted">Tỷ Lệ Mở</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-warning">{{ $campaign->stats['click_rate'] }}%</h4>
                            <small class="text-muted">Tỷ Lệ Click</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.alert-info {
    background-color: #e7f3ff;
    border-color: #b8daff;
    color: #004085;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
}

.card-header h6 {
    color: #5a5c69;
}

.btn-primary {
    background: linear-gradient(45deg, #4e73df, #224abe);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #224abe, #1e3a8a);
}
</style>
@endpush
