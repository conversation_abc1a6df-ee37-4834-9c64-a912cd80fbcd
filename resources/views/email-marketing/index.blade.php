@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-envelope text-primary me-2"></i>
                        Email Marketing Dashboard
                    </h1>
                    <p class="text-muted mb-0">Quản lý và theo dõi hiệu quả email marketing</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.campaigns.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Tạo Campaign Mới
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Tổng Campaigns
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $totalCampaigns }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-bullhorn fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Campaigns Đang Chạy
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $activeCampaigns }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-play fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Tổng Email Đã Gửi
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($totalEmailsSent) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-paper-plane fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Tỷ Lệ Mở Email
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $openRate }}%</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-envelope-open fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-rocket me-2"></i>Thao Tác Nhanh
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('email-marketing.campaigns.create') }}" class="btn btn-outline-primary btn-block">
                                        <i class="fas fa-plus me-2"></i>Tạo Campaign
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('email-marketing.templates.create') }}" class="btn btn-outline-success btn-block">
                                        <i class="fas fa-file-alt me-2"></i>Tạo Template
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('email-marketing.campaigns') }}" class="btn btn-outline-info btn-block">
                                        <i class="fas fa-list me-2"></i>Xem Campaigns
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('email-marketing.templates') }}" class="btn btn-outline-warning btn-block">
                                        <i class="fas fa-envelope me-2"></i>Quản Lý Templates
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Campaigns and Logs -->
            <div class="row">
                <!-- Recent Campaigns -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-bullhorn me-2"></i>Campaigns Gần Đây
                            </h6>
                            <a href="{{ route('email-marketing.campaigns') }}" class="btn btn-sm btn-outline-primary">
                                Xem Tất Cả
                            </a>
                        </div>
                        <div class="card-body">
                            @if($recentCampaigns->count() > 0)
                                @foreach($recentCampaigns as $campaign)
                                    <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <a href="{{ route('email-marketing.campaigns.show', $campaign) }}" class="text-decoration-none">
                                                    {{ $campaign->name }}
                                                </a>
                                            </h6>
                                            <small class="text-muted">
                                                @if($campaign->webinar)
                                                    <i class="fas fa-video me-1"></i>{{ $campaign->webinar->title }}
                                                @endif
                                                <span class="ms-2">{{ $campaign->created_at->diffForHumans() }}</span>
                                            </small>
                                        </div>
                                        <div>
                                            <span class="badge badge-{{ $campaign->status === 'active' ? 'success' : ($campaign->status === 'draft' ? 'secondary' : 'warning') }}">
                                                {{ ucfirst($campaign->status) }}
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-bullhorn fa-3x text-gray-300 mb-3"></i>
                                    <p class="text-muted">Chưa có campaign nào</p>
                                    <a href="{{ route('email-marketing.campaigns.create') }}" class="btn btn-primary">
                                        Tạo Campaign Đầu Tiên
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Recent Email Logs -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-history me-2"></i>Hoạt Động Gần Đây
                            </h6>
                        </div>
                        <div class="card-body">
                            @if($recentLogs->count() > 0)
                                @foreach($recentLogs->take(5) as $log)
                                    <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">{{ $log->subject }}</h6>
                                            <small class="text-muted">
                                                <i class="fas fa-envelope me-1"></i>{{ $log->recipient_email }}
                                                <span class="ms-2">{{ $log->created_at->diffForHumans() }}</span>
                                            </small>
                                        </div>
                                        <div>
                                            <span class="badge badge-{{ $log->getStatusBadgeColor() }}">
                                                {{ $log->getStatusText() }}
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-history fa-3x text-gray-300 mb-3"></i>
                                    <p class="text-muted">Chưa có hoạt động nào</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.btn-block {
    width: 100%;
}
</style>
@endpush
