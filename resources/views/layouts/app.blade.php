<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF <PERSON> -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ \App\Models\Setting::get('site_title', config('app.name', 'Webinar Platform')) }}</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Inter:400,500,600,700" rel="stylesheet">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">

    <!-- Custom Tabs CSS -->
    <link rel="stylesheet" href="{{ asset('css/tabs.css') }}">

    <!-- Pagination CSS -->
    <link rel="stylesheet" href="{{ asset('css/pagination.css') }}">

    <!-- Notify CSS -->
    @notifyCss

    <!-- Header Scripts from Settings -->
    {!! \App\Models\Setting::get('header_scripts') !!}

    <!-- Favicon -->
    @if(\App\Models\Setting::get('favicon'))
        <link rel="icon" href="{{ Storage::url(\App\Models\Setting::get('favicon')) }}" type="image/x-icon">
    @endif

    <!-- Custom Styles -->
    <style>
        .modal-footer .btn-secondary {
            color: #000;
        }

        :root {
            --primary-color: #fa8128;
            --primary-hover: #e67420;
            --primary-light: #fff6ef;
            --primary-transparent: rgba(250, 129, 40, 0.1);
            --danger-color: #FA5959;
            --danger-light: rgba(250, 89, 89, 0.1);
            --success-color: #27B469;
            --success-light: rgba(39, 180, 105, 0.1);
            --warning-color: #FFC600;
            --warning-light: rgba(255, 198, 0, 0.1);
            --secondary-color: #6B7A99;
            --secondary-light: rgba(107, 122, 153, 0.1);
            --light-bg: #F4F7FC;
            --text-color: #3E4B64;
            --text-muted: #6B7A99;
            --border-color: #eaedf2;
            --card-border-radius: 10px;
            --card-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            --btn-shadow: 0 3px 5px rgba(250, 129, 40, 0.2);
            --transition: all 0.25s ease;
        }

        /* Override Bootstrap color classes */
        .bg-primary {
            background-color: var(--primary-color) !important;
        }

        .bg-success {
            background-color: var(--success-color) !important;
        }

        .bg-danger {
            background-color: var(--danger-color) !important;
        }

        .bg-warning {
            background-color: var(--warning-color) !important;
        }

        .bg-secondary {
            background-color: var(--secondary-color) !important;
        }

        /* Text colors */
        .text-primary {
            color: var(--primary-color) !important;
        }

        .text-success {
            color: var(--success-color) !important;
        }

        .text-danger {
            color: var(--danger-color) !important;
        }

        .text-warning {
            color: var(--warning-color) !important;
        }

        .text-secondary {
            color: var(--secondary-color) !important;
        }

        .text-muted {
            color: var(--text-muted) !important;
        }

        /* SweetAlert2 button styles - fix for visibility */
        .swal2-actions .swal2-confirm,
        .swal2-actions .swal2-cancel {
            color: #fff !important;
            font-weight: 600 !important;
            padding: 8px 24px !important;
            font-size: 14px !important;
            text-shadow: none !important;
            box-shadow: var(--btn-shadow) !important;
            border-radius: 8px !important;
        }

        .swal2-actions .swal2-confirm.swal2-styled {
            background-color: var(--danger-color) !important;
        }

        .swal2-actions .swal2-cancel.swal2-styled {
            background-color: var(--primary-color) !important;
        }

        /* Ensure text is visible in all SweetAlert2 modals */
        .swal2-popup {
            background-color: #fff !important;
            border-radius: 12px !important;
        }

        .swal2-title {
            color: var(--text-color) !important;
            font-weight: 600 !important;
        }

        .swal2-html-container {
            color: var(--text-color) !important;
        }

        /* Specific style for video confirmation popup */
        .video-confirm-popup .swal2-title,
        .video-confirm-popup .swal2-html-container {
            color: var(--text-color) !important;
            font-weight: normal !important;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            margin: 0;
            padding: 0;
            color: var(--text-color);
            font-size: 0.95rem;
            line-height: 1.5;
        }

        /* Navbar Styles */
        .top-navbar {
            background-color: var(--primary-color);
            padding: 8px 0;
            box-shadow: 0 2px 10px rgba(250, 129, 40, 0.2);
            color: #fff;
        }

        .top-navbar .navbar-brand {
            color: #fff;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        .top-navbar .nav-link {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            padding: 0.5rem 0.75rem;
            margin: 0 0.3rem;
            position: relative;
            min-width: auto;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            transition: var(--transition);
            white-space: nowrap;
            font-size: 15px;
        }

        .top-navbar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        .top-navbar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .top-navbar .nav-link .badge {
            position: absolute;
            top: 0;
            right: 0;
            font-size: 0.65rem;
            transform: translate(25%, -25%);
        }

        /* Sidebar Styles */
        .sidebar {
            background-color: #fff;
            border-radius: var(--card-border-radius);
            box-shadow: var(--card-shadow);
            min-height: calc(100vh - 56px - 2rem);
        }

        .sidebar .nav-link {
            color: var(--text-muted);
            padding: 0.75rem 1.25rem;
            font-weight: 500;
            transition: var(--transition);
            margin: 2px 0;
            border-radius: 6px;
            display: flex;
            align-items: center;
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
            opacity: 0.7;
        }

        .sidebar .nav-link.active {
            color: var(--primary-color);
            background-color: var(--primary-light);
            font-weight: 600;
        }

        .sidebar .nav-link.active i {
            opacity: 1;
        }

        .sidebar .nav-link:hover {
            color: var(--primary-color);
            background-color: var(--primary-light);
        }

        /* Card Styles */
        .card {
            border-radius: var(--card-border-radius);
            box-shadow: var(--card-shadow);
            border: none;
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: var(--transition);
        }

        .card:hover {
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 1.25rem;
            font-weight: 600;
        }

        .card-header.bg-primary,
        .card-header.bg-success,
        .card-header.bg-danger,
        .card-header.bg-warning,
        .card-header.bg-secondary {
            color: white;
            border-bottom: none;
        }

        /* Stats Cards */
        .stats-card {
            border-radius: var(--card-border-radius);
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            transition: var(--transition);
        }

        .stats-card:hover {
            transform: translateY(-3px);
        }

        .stats-card .stats-icon {
            position: absolute;
            top: 50%;
            right: 1.5rem;
            transform: translateY(-50%);
            opacity: 0.2;
            font-size: 4rem;
        }

        .stats-card .card-title {
            font-size: 0.85rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.02em;
        }

        .stats-card .stats-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0;
        }

        .stats-card.bg-primary {
            background-color: var(--primary-color);
        }

        .stats-card.bg-primary-light {
            background-color: var(--primary-light);
        }

        .stats-card.bg-primary-light .card-title,
        .stats-card.bg-primary-light .stats-value,
        .stats-card.bg-primary-light .stats-icon {
            color: var(--primary-color);
        }

        .stats-card.bg-success {
            background-color: var(--success-color);
        }

        .stats-card.bg-success-light {
            background-color: var(--success-light);
        }

        .stats-card.bg-success-light .card-title,
        .stats-card.bg-success-light .stats-value,
        .stats-card.bg-success-light .stats-icon {
            color: var(--success-color);
        }

        .stats-card.bg-danger {
            background-color: var(--danger-color);
        }

        .stats-card.bg-warning {
            background-color: var(--warning-color);
        }

        /* Button Styles */
        .btn {
            font-weight: 500;
            border-radius: 6px;
            transition: var(--transition);
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }

        .btn:focus,
        .btn:active {
            box-shadow: none !important;
        }

        .btn-sm {
            font-size: 0.78rem;
            padding: 0.4rem 0.8rem;
        }

        .btn i {
            font-size: 0.9em;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            box-shadow: var(--btn-shadow);
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(250, 129, 40, 0.25);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
            box-shadow: 0 3px 5px rgba(39, 180, 105, 0.2);
        }

        .btn-success:hover {
            background-color: #239c5a;
            border-color: #239c5a;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(39, 180, 105, 0.25);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            box-shadow: 0 3px 5px rgba(250, 89, 89, 0.2);
        }

        .btn-danger:hover {
            background-color: #e64c4c;
            border-color: #e64c4c;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(250, 89, 89, 0.25);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
            color: #fff;
            box-shadow: 0 3px 5px rgba(255, 198, 0, 0.2);
        }

        .btn-warning:hover {
            background-color: #e6b400;
            border-color: #e6b400;
            color: #fff;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 198, 0, 0.25);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: #fff;
            box-shadow: 0 3px 5px rgba(107, 122, 153, 0.2);
        }

        .btn-secondary:hover {
            background-color: #5e6c87;
            border-color: #5e6c87;
            color: #fff;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(107, 122, 153, 0.25);
        }

        .btn-info {
            background-color: #3498db;
            border-color: #3498db;
            color: #fff;
            box-shadow: 0 3px 5px rgba(52, 152, 219, 0.2);
        }

        .btn-info:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            color: #fff;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.25);
        }

        .btn-light {
            background-color: #f8f9fa;
            border-color: #f8f9fa;
            color: var(--text-color);
            box-shadow: 0 3px 5px rgba(0, 0, 0, 0.05);
        }

        .btn-light:hover {
            background-color: #e9ecef;
            border-color: #e9ecef;
            color: var(--text-color);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        }

        /* Date Display */
        .date-display {
            background-color: #fff;
            padding: 0.5rem 1rem;
            border-radius: 30px;
            box-shadow: var(--card-shadow);
            display: inline-flex;
            align-items: center;
            color: var(--text-muted);
            font-weight: 500;
        }

        .date-display i {
            margin-right: 0.5rem;
            color: var(--primary-color);
        }

        /* Avatar */
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            color: #fff;
            font-weight: 600;
            font-size: 1rem;
        }

        .user-name {
            color: #fff;
            font-weight: 600;
        }

        /* Table Styles */
        .table {
            color: var(--text-color);
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background-color: rgba(244, 247, 252, 0.75);
            color: var(--text-muted);
            font-weight: 600;
            border-top: none;
            padding: 1rem;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.03em;
        }

        .table td {
            padding: 1rem;
            vertical-align: middle;
            border-top: 1px solid var(--border-color);
        }

        .table-hover tbody tr:hover {
            background-color: var(--primary-light);
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(244, 247, 252, 0.5);
        }

        /* Badge */
        .badge {
            padding: 0.4em 0.65em;
            font-weight: 600;
            border-radius: 30px;
            font-size: 0.75rem;
            letter-spacing: 0.03em;
        }

        .badge.bg-primary {
            background-color: var(--primary-color) !important;
        }

        .badge.bg-success {
            background-color: var(--success-color) !important;
        }

        .badge.bg-danger {
            background-color: var(--danger-color) !important;
        }

        .badge.bg-warning {
            background-color: var(--warning-color) !important;
        }

        .badge.bg-secondary {
            background-color: var(--secondary-color) !important;
        }

        .badge-primary {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .badge-success {
            background-color: var(--success-light);
            color: var(--success-color);
        }

        .badge-danger {
            background-color: var(--danger-light);
            color: var(--danger-color);
        }

        .badge-warning {
            background-color: var(--warning-light);
            color: var(--warning-color);
        }

        .badge-secondary {
            background-color: var(--secondary-light);
            color: var(--secondary-color);
        }

        /* Form Controls */
        .form-control,
        .form-select {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 0.5rem 0.75rem;
            font-size: 0.95rem;
            transition: var(--transition);
        }

        .form-control:focus,
        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(250, 129, 40, 0.15);
        }

        .input-group .btn {
            z-index: 0;
        }

        .input-group-text {
            background-color: #f8f9fa;
            border-color: var(--border-color);
            color: var(--text-muted);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #CCD7F0;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #A3BCEA;
        }

        /* Dashboard specific */
        .main-title {
            font-size: 1.65rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: var(--text-color);
            display: flex;
            align-items: center;
        }

        .main-title i {
            background-color: var(--primary-color);
            color: #fff;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
            font-size: 1.25rem;
        }

        /* Make top navbar sticky */
        .top-navbar {
            position: sticky;
            top: 0;
            z-index: 100;
        }

        /* Content padding */
        .main-content {
            padding-top: 1.5rem;
            padding-bottom: 1.5rem;
        }

        /* Currency */
        .currency {
            font-size: 1rem;
            font-weight: 400;
        }

        /* Chart container */
        .chart-container {
            background-color: #fff;
            border-radius: var(--card-border-radius);
            box-shadow: var(--card-shadow);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        /* List group styles */
        .list-group-item {
            border-color: var(--border-color);
            padding: 0.75rem 1rem;
        }

        .list-group-item:first-child {
            border-top-left-radius: var(--card-border-radius);
            border-top-right-radius: var(--card-border-radius);
        }

        .list-group-item:last-child {
            border-bottom-left-radius: var(--card-border-radius);
            border-bottom-right-radius: var(--card-border-radius);
        }

        /* Alert styles */
        .alert {
            border-radius: 8px;
            border: none;
            padding: 1rem;
        }

        .alert-primary {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .alert-success {
            background-color: var(--success-light);
            color: var(--success-color);
        }

        .alert-danger {
            background-color: var(--danger-light);
            color: var(--danger-color);
        }

        .alert-warning {
            background-color: var(--warning-light);
            color: var(--warning-color);
        }

        .alert-info {
            background-color: rgba(41, 128, 185, 0.1);
            color: #2980b9;
        }

        /* Select2 tags style for Bootstrap 5 - hiện đại, mềm mại */
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
            background: var(--primary-light);
            border: 1px solid rgba(250, 129, 40, 0.2);
            color: var(--primary-color);
            padding: 0.15em 0.7em 0.15em 0.7em;
            margin-top: 0.25rem;
            margin-right: 0.25rem;
            border-radius: 12px;
            font-size: 0.9em;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            min-width: 0;
            max-width: 100%;
            box-shadow: 0 1px 2px rgba(250, 129, 40, 0.04);
            transition: var(--transition);
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove {
            color: var(--primary-color);
            margin-right: 0.4em;
            font-weight: bold;
            opacity: 0.6;
            font-size: 1em;
            transition: var(--transition);
            cursor: pointer;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: var(--danger-color);
            opacity: 1;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple {
            min-height: 38px;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            background: #fff;
            box-shadow: none;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            cursor: text;
        }

        .select2-container--bootstrap-5 .select2-search--inline .select2-search__field {
            width: auto !important;
            min-width: 120px;
            font-size: 0.95em;
            margin-top: 0.25rem;
            margin-bottom: 0.25rem;
            border: none;
            outline: none;
            box-shadow: none;
        }

        /* Select2 single style for Bootstrap 5 */
        .select2-container {
            width: 100% !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single {
            height: 38px !important;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
        }

        .select2-container--bootstrap-5 .select2-selection__rendered {
            line-height: 38px !important;
        }

        /* Footer */
        footer {
            background-color: white;
            border-top: 1px solid var(--border-color);
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        /* Navbar responsive styles */
        @media (max-width: 991px) {
            .top-navbar .nav-link {
                padding: 0.4rem 0.6rem;
                margin: 0 0.05rem;
                font-size: 0.85rem;
            }

            .container-fluid {
                padding-left: 0.5rem !important;
                padding-right: 0.5rem !important;
            }

            /* Dropdown menu responsive */
            .navbar .dropdown-menu {
                position: static !important;
                transform: none !important;
                box-shadow: none;
                border: 1px solid var(--border-color);
                margin-top: 0.25rem;
                background-color: rgba(255, 255, 255, 0.95);
            }
        }

        @media (max-width: 767px) {
            .top-navbar .nav-link {
                text-align: left;
                justify-content: flex-start;
                padding: 0.5rem 1rem;
                margin: 0.2rem 0;
            }

            .top-navbar .navbar-collapse {
                margin-top: 0.5rem;
                padding: 0.5rem;
                background-color: var(--primary-hover);
                border-radius: 8px;
            }

            .main-title {
                font-size: 1.35rem;
            }

            .main-title i {
                width: 36px;
                height: 36px;
                font-size: 1.1rem;
            }

            /* Mobile dropdown styles */
            .navbar .dropdown-menu {
                background-color: rgba(255, 255, 255, 0.98);
                margin-left: 1rem;
                margin-right: 1rem;
                border-radius: 6px;
            }

            .navbar .dropdown-item {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }
        }

        /* Dropdown menu styles */
        .navbar .dropdown-menu {
            background-color: #fff;
            border: none;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            margin-top: 0.5rem;
            padding: 0.5rem 0;
            min-width: 200px;
        }

        .navbar .dropdown-item {
            color: var(--text-color);
            padding: 0.6rem 1.25rem;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
            border-radius: 0;
            position: relative;
        }

        .navbar .dropdown-item:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .navbar .dropdown-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: 600;
        }

        .navbar .dropdown-item i {
            width: 18px;
            text-align: center;
            opacity: 0.7;
        }

        .navbar .dropdown-item:hover i,
        .navbar .dropdown-item.active i {
            opacity: 1;
        }

        /* Badge trong dropdown item */
        .navbar .dropdown-item .badge {
            margin-left: auto;
            font-size: 0.65rem;
        }

        /* Dropdown toggle caret */
        .navbar .dropdown-toggle::after {
            margin-left: 0.5rem;
        }

        /* Badge trong nav-link dropdown toggle */
        .navbar .nav-link .badge {
            position: absolute;
            top: -2px;
            right: -8px;
            font-size: 0.65rem;
            transform: none;
        }
    </style>

    @stack('styles')
</head>

<body>
    <div id="app">
        <nav class="navbar navbar-expand-md top-navbar">
            <div class="container-fluid px-3" style="max-width: 1600px;">

                @can('dashboard')
                    <a class="navbar-brand d-flex align-items-center" href="{{ url('/') }}">
                        @if(\App\Models\Setting::get('admin_logo'))
                            <img src="{{ Storage::url(\App\Models\Setting::get('admin_logo')) }}"
                                alt="{{ \App\Models\Setting::get('site_title') }}" height="36" class="me-2">
                        @else
                            <i class="fas fa-video-camera me-2"></i>
                            {{ \App\Models\Setting::get('site_title', config('app.name', 'Webinar Platform')) }}
                        @endif

                    </a>
                @else
                    <a class="navbar-brand d-flex align-items-center" href="{{ route('telesales.dashboard') }}">
                        @if(\App\Models\Setting::get('admin_logo'))
                            <img src="{{ Storage::url(\App\Models\Setting::get('admin_logo')) }}"
                                alt="{{ \App\Models\Setting::get('site_title') }}" height="36" class="me-2">
                        @else
                            <i class="fas fa-video-camera me-2"></i>
                            {{ \App\Models\Setting::get('site_title', config('app.name', 'Webinar Platform')) }}
                        @endif

                    </a>
                @endcan

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                    data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                    aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Left Side Of Navbar - Main Menu -->
                    <ul class="navbar-nav me-auto">
                        @auth

                            @can('dashboard')
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}"
                                        href="{{ route('home') }}">
                                        <i class="fas fa-tachometer-alt me-1"></i> Bảng điều khiển
                                    </a>
                                </li>
                            @endcan

                            <!-- Webinar - Menu độc lập -->
                            @can('webinars index')
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('webinars.*') ? 'active' : '' }}"
                                        href="{{ route('webinars.index') }}">
                                        <i class="fas fa-video me-1"></i> Webinar
                                    </a>
                                </li>
                            @endcan

                            <!-- Nhóm Dạy Học -->
                            @can('questions index')
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle {{ request()->routeIs(['question-sets.*']) ? 'active' : '' }}"
                                        href="#" id="teachingDropdown" role="button" data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                        <i class="fas fa-graduation-cap me-1"></i> Dạy Học
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="teachingDropdown">
                                        <li>
                                            <a class="dropdown-item {{ request()->routeIs('question-sets.*') ? 'active' : '' }}"
                                                href="{{ route('question-sets.index') }}">
                                                <i class="fas fa-question-circle me-2"></i> Câu hỏi
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            @endcan

                            <!-- Nhóm Bán hàng -->
                            @if(auth()->user()->can('product index') || auth()->user()->can('order index') || auth()->user()->can('telesales'))
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle {{ request()->routeIs(['products.*', 'orders.*', 'telesales.*']) ? 'active' : '' }}"
                                        href="#" id="salesDropdown" role="button" data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                        <i class="fas fa-shopping-bag me-1"></i> Bán hàng
                                        @if(auth()->check() && auth()->user()->can('view telesales') && isset($teleSaleReminders) && $teleSaleReminders > 0)
                                            <span class="badge bg-danger rounded-pill">{{ $teleSaleReminders }}</span>
                                        @endif
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="salesDropdown">
                                        @can('product index')
                                            <li>
                                                <a class="dropdown-item {{ request()->routeIs('products.*') ? 'active' : '' }}"
                                                    href="{{ route('products.index') }}">
                                                    <i class="fas fa-box-open me-2"></i> Sản phẩm
                                                </a>
                                            </li>
                                        @endcan
                                        @can('order index')
                                            <li>
                                                <a class="dropdown-item {{ request()->routeIs('orders.*') ? 'active' : '' }}"
                                                    href="{{ route('orders.index') }}">
                                                    <i class="fas fa-shopping-cart me-2"></i> Order
                                                </a>
                                            </li>
                                        @endcan
                                        @can('telesales')
                                            <li>
                                                <a class="dropdown-item {{ request()->routeIs('telesales.*') ? 'active' : '' }}"
                                                    href="{{ route('telesales.dashboard') }}">
                                                    <i class="fas fa-headset me-2"></i> Telesales
                                                    @if(auth()->check() && auth()->user()->can('view telesales') && isset($teleSaleReminders) && $teleSaleReminders > 0)
                                                        <span class="badge bg-danger rounded-pill ms-1">{{ $teleSaleReminders }}</span>
                                                    @endif
                                                </a>
                                            </li>
                                        @endcan
                                    </ul>
                                </li>
                            @endif

                            <!-- Nhóm Marketing -->
                            @if(auth()->user()->can('advertisements index') || auth()->user()->can('webinars index') || auth()->user()->can('email marketing'))
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle {{ request()->routeIs(['advertisements.*', 'smart-links.*', 'email-marketing.*']) ? 'active' : '' }}"
                                        href="#" id="marketingDropdown" role="button" data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                        <i class="fas fa-bullhorn me-1"></i> Marketing
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="marketingDropdown">
                                        @can('email marketing')
                                            <li>
                                                <a class="dropdown-item {{ request()->routeIs('email-marketing.*') ? 'active' : '' }}"
                                                    href="{{ route('email-marketing.index') }}">
                                                    <i class="fas fa-envelope me-2"></i> Email Marketing
                                                </a>
                                            </li>
                                        @endcan
                                        @can('advertisements index')
                                            <li>
                                                <a class="dropdown-item {{ request()->routeIs('advertisements.*') ? 'active' : '' }}"
                                                    href="{{ route('advertisements.index') }}">
                                                    <i class="fas fa-bullhorn me-2"></i> Quảng cáo
                                                </a>
                                            </li>
                                        @endcan
                                        @can('webinars index')
                                            <li>
                                                <a class="dropdown-item {{ request()->routeIs('smart-links.*') ? 'active' : '' }}"
                                                    href="{{ route('smart-links.index') }}">
                                                    <i class="fas fa-link me-2"></i> Link Smart
                                                </a>
                                            </li>
                                        @endcan
                                    </ul>
                                </li>
                            @endif
                            <!-- Utilities Menu - Show enabled modules -->
                            @php
                                $enabledModules = getEnabledModules()->filter(function ($module) {
                                    return $module->hasPermission();
                                });
                            @endphp



                            @if(auth()->user()->can('users index') || auth()->user()->can('roles index') || auth()->user()->can('settings'))
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle {{ request()->routeIs(['admin.users.*', 'admin.roles.*', 'settings.*']) ? 'active' : '' }}"
                                        href="#" id="settingsDropdown" role="button" data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                        <i class="fas fa-cog me-1"></i> Cài đặt
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="settingsDropdown">
                                        @can('settings')
                                            <li>
                                                <a class="dropdown-item {{ request()->routeIs('settings.*') ? 'active' : '' }}"
                                                    href="{{ route('settings.index') }}">
                                                    <i class="fas fa-tools me-2"></i> Cài đặt chung
                                                </a>
                                            </li>

                                        @endcan

                                            @can('settings')
                                                <li class="nav-item">
                                                    <a class="dropdown-item {{ request()->routeIs('admin.modules.*') ? 'active' : '' }}"
                                                       href="{{ route('admin.modules.index') }}">
                                                        <i class="fas fa-puzzle-piece me-1"></i> Tiện ích bổ sung
                                                    </a>
                                                </li>
                                            @endcan
                                        @can('users index')
                                            <li>
                                                <a class="dropdown-item {{ request()->routeIs('admin.users.*') ? 'active' : '' }}"
                                                    href="{{ route('admin.users.index') }}">
                                                    <i class="fas fa-users me-2"></i> Người dùng
                                                </a>
                                            </li>
                                        @endcan
                                        @can('roles index')
                                            <li>
                                                <a class="dropdown-item {{ request()->routeIs('admin.roles.*') ? 'active' : '' }}"
                                                    href="{{ route('admin.roles.index') }}">
                                                    <i class="fas fa-users-cog me-2"></i> Phân quyền
                                                </a>
                                            </li>
                                        @endcan
                                    </ul>
                                </li>
                            @endif
                        @endauth
                    </ul>

                    <!-- Right Side Of Navbar -->
                    <ul class="navbar-nav ms-auto">
                        <!-- Authentication Links -->
                        @guest
                            @if (Route::has('login'))
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('login') }}">Đăng Nhập</a>
                                </li>
                            @endif
                        @else
                            <li class="nav-item dropdown">
                                <a id="navbarDropdown" class="nav-link dropdown-toggle d-flex align-items-center" href="#"
                                    role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
                                    v-pre>
                                    <div class="user-avatar">
                                        {{ substr(Auth::user()->name, 0, 1) }}
                                    </div>
                                    <span class="user-name">{{ Auth::user()->name }}</span>
                                </a>

                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <a class="dropdown-item" href="{{ route('profile.edit') }}">
                                        <i class="fas fa-user me-2"></i> Hồ sơ
                                    </a>
                                    <a class="dropdown-item" href="{{ route('settings.index') }}">
                                        <i class="fas fa-cog me-2"></i> Cài đặt
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="{{ route('logout') }}" onclick="event.preventDefault();
                                                         document.getElementById('logout-form').submit();">
                                        <i class="fas fa-sign-out-alt me-2"></i> Đăng Xuất
                                    </a>

                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                        @csrf
                                    </form>
                                </div>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>

        <main class="main-content">
            <!-- Notify component -->
            <x-notify::notify />

            @auth
                <div class="container-fluid px-4">
                    @if(false)
                        <!-- Ẩn sidebar -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="sidebar p-3">
                                    <ul class="nav flex-column">

                                        @can('dashboard')
                                            <li class="nav-item">
                                                <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}"
                                                    href="{{ route('home') }}">
                                                    <i class="fas fa-tachometer-alt"></i> Bảng Điều Khiển
                                                </a>
                                            </li>
                                        @endcan
                                        @can('webinars index')
                                            <li class="nav-item">
                                                <a class="nav-link {{ request()->routeIs('webinars.*') ? 'active' : '' }}"
                                                    href="{{ route('webinars.index') }}">
                                                    <i class="fas fa-video"></i> Webinar
                                                </a>
                                            </li>
                                        @endcan
                                        @can('users index')
                                            <li class="nav-item">
                                                <a class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}"
                                                    href="{{ route('admin.users.index') }}">
                                                    <i class="fas fa-users"></i> Người Dùng
                                                </a>
                                            </li>
                                        @endcan
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-9">
                                @if (session('success'))
                                    <div class="alert alert-success">
                                        {{ session('success') }}
                                    </div>
                                @endif

                                @if (session('error'))
                                    <div class="alert alert-danger">
                                        {{ session('error') }}
                                    </div>
                                @endif

                                @yield('content')
                            </div>
                        </div>
                    @else
                        <div class="row">
                            <div class="col-12">
                                {{-- @if (session('success'))--}}
                                {{-- <div class="alert alert-success">--}}
                                    {{-- {{ session('success') }}--}}
                                    {{-- </div>--}}
                                {{-- @endif--}}

                                {{-- @if (session('error'))--}}
                                {{-- <div class="alert alert-danger">--}}
                                    {{-- {{ session('error') }}--}}
                                    {{-- </div>--}}
                                {{-- @endif--}}

                                @yield('content')
                            </div>
                        </div>
                    @endif
                </div>
            @else
                <div class="container">
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    @yield('content')
                </div>
            @endauth
        </main>

        <footer class="bg-light py-3 border-top mt-5">
            <div class="container text-center">
                <p class="mb-0 text-muted">
                    {!! \App\Models\Setting::get('footer_text', '© ' . date('Y') . ' Hệ Thống Quản Lý Webinar. Tất cả quyền được bảo lưu.') !!}
                </p>
            </div>
        </footer>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Notify JS -->
    @notifyJs

    <script>
        // Hàm hiển thị thông báo SweetAlert2
        function showAlert(title, message, type, timer = 10000) {
            // Kiểm tra nếu message chứa HTML list hoặc không
            const isHtmlList = message.includes('<ul') || message.includes('<li');

            let finalMessage = message;
            if (!isHtmlList && type === 'error') {
                // Nếu là thông báo lỗi nhưng không phải dạng danh sách HTML, thêm style để căn lề trái
                finalMessage = '<div class="text-start">' + message + '</div>';
            }

            Swal.fire({
                title: title,
                html: finalMessage,
                icon: type, // 'success', 'error', 'warning', 'info', 'question'
                timer: timer,
                timerProgressBar: true,
                showConfirmButton: true,
                confirmButtonText: 'Đã hiểu',
                confirmButtonColor: '#4176F9'
            });
        }

        // SweetAlert2 cho thông báo flash session
        document.addEventListener('DOMContentLoaded', function () {
            @if(session('success'))
                showAlert('Thành công', '{!! session('success') !!}', 'success');
            @endif

            @if(session('error'))
                showAlert('Lỗi', '{!! session('error') !!}', 'error');
            @endif

            @if(session('warning'))
                showAlert('Cảnh báo', '{!! session('warning') !!}', 'warning');
            @endif

            @if(session('info'))
                showAlert('Thông tin', '{!! session('info') !!}', 'info');
            @endif
    });

        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function () {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>

    <!-- Pagination JS -->
    <script src="{{ asset('js/pagination.js') }}"></script>

    <!-- Notification JS -->
    <x-notify::notify />
    @notifyJs

    @stack('scripts')

    @include('sweetalert::alert')

    <!-- Footer Scripts from Settings -->
    {!! \App\Models\Setting::get('footer_scripts') !!}
</body>

</html>
