<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('campaign_id')->constrained('email_campaigns')->onDelete('cascade');
            $table->foreignId('workflow_id')->constrained('email_workflows')->onDelete('cascade');
            $table->foreignId('webinar_participant_id')->constrained()->onDelete('cascade');
            $table->string('recipient_email');
            $table->string('recipient_name')->nullable();
            $table->string('subject');
            $table->longText('content'); // Nội dung email đã gửi
            $table->enum('status', ['pending', 'sent', 'failed', 'bounced', 'delivered', 'opened', 'clicked'])->default('pending');
            $table->text('error_message')->nullable(); // Lỗi nếu gửi thất bại
            $table->timestamp('scheduled_at')->nullable(); // Thời gian dự kiến gửi
            $table->timestamp('sent_at')->nullable(); // Thời gian gửi thực tế
            $table->timestamp('delivered_at')->nullable(); // Thời gian delivered
            $table->timestamp('opened_at')->nullable(); // Thời gian mở email
            $table->timestamp('clicked_at')->nullable(); // Thời gian click link
            $table->integer('open_count')->default(0); // Số lần mở
            $table->integer('click_count')->default(0); // Số lần click
            $table->json('tracking_data')->nullable(); // Dữ liệu tracking khác
            $table->timestamps();

            $table->index(['campaign_id', 'status']);
            $table->index(['webinar_participant_id']);
            $table->index(['recipient_email']);
            $table->index(['scheduled_at']);
            $table->index(['sent_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_logs');
    }
};
