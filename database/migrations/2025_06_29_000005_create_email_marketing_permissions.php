<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create email marketing permission
        if (!Permission::where('name', 'email marketing')->exists()) {
            Permission::create(['name' => 'email marketing', 'guard_name' => 'web']);
        }

        // Assign permission to admin role if exists
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole && !$adminRole->hasPermissionTo('email marketing')) {
            $adminRole->givePermissionTo('email marketing');
        }

        // Assign permission to super-admin role if exists
        $superAdminRole = Role::where('name', 'super-admin')->first();
        if ($superAdminRole && !$superAdminRole->hasPermissionTo('email marketing')) {
            $superAdminRole->givePermissionTo('email marketing');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Remove permission from roles
        $permission = Permission::where('name', 'email marketing')->first();
        if ($permission) {
            $permission->delete();
        }
    }
};
