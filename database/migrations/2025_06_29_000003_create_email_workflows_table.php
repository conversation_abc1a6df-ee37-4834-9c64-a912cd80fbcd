<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_workflows', function (Blueprint $table) {
            $table->id();
            $table->foreignId('campaign_id')->constrained('email_campaigns')->onDelete('cascade');
            $table->foreignId('email_template_id')->constrained()->onDelete('cascade');
            $table->integer('sequence_order')->default(1); // Thứ tự trong workflow
            $table->integer('delay_days')->default(0); // Gửi sau bao nhiêu ngày
            $table->integer('delay_hours')->default(0); // Gửi sau bao nhiêu giờ
            $table->integer('delay_minutes')->default(0); // Gửi sau bao nhiêu phút
            $table->json('conditions')->nullable(); // Đi<PERSON>u kiện để gửi email này
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['campaign_id', 'sequence_order']);
            $table->index(['email_template_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_workflows');
    }
};
