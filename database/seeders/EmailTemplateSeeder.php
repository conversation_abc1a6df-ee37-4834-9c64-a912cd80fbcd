<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\EmailTemplate;
use App\Models\User;

class EmailTemplateSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Get first admin user
        $adminUser = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['admin', 'super-admin']);
        })->first();

        if (!$adminUser) {
            $this->command->warn('No admin user found. Skipping email template seeding.');
            return;
        }

        $templates = [
            [
                'name' => 'Welcome Email - Đăng ký thành công',
                'subject' => 'Chào mừng {{participant_name}} đến với {{webinar_title}}!',
                'content' => $this->getWelcomeEmailContent(),
                'template_type' => 'welcome',
                'is_default' => true,
            ],
            [
                'name' => 'Reminder <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>c nhở trước webinar',
                'subject' => 'Nhắc nhở: {{webinar_title}} sẽ diễn ra {{time_until_webinar}}',
                'content' => $this->getReminderEmailContent(),
                'template_type' => 'reminder',
                'is_default' => true,
            ],
            [
                'name' => 'Follow-up Email - Cảm ơn sau webinar',
                'subject' => 'Cảm ơn bạn đã tham gia {{webinar_title}}',
                'content' => $this->getFollowUpEmailContent(),
                'template_type' => 'follow_up',
                'is_default' => true,
            ],
            [
                'name' => 'Promotional Email - Khuyến mãi đặc biệt',
                'subject' => 'Ưu đãi đặc biệt dành cho bạn từ {{site_name}}',
                'content' => $this->getPromotionalEmailContent(),
                'template_type' => 'promotional',
                'is_default' => true,
            ],
        ];

        foreach ($templates as $templateData) {
            EmailTemplate::updateOrCreate(
                [
                    'name' => $templateData['name'],
                    'user_id' => $adminUser->id,
                ],
                array_merge($templateData, [
                    'user_id' => $adminUser->id,
                    'variables' => $this->getDefaultVariables(),
                ])
            );
        }

        $this->command->info('Email templates seeded successfully!');
    }

    private function getWelcomeEmailContent()
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chào mừng đến với webinar</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(45deg, #4e73df, #224abe); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f8f9fc; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #1cc88a; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Chào mừng {{participant_name}}!</h1>
            <p>Cảm ơn bạn đã đăng ký tham gia webinar của chúng tôi</p>
        </div>
        <div class="content">
            <h2>{{webinar_title}}</h2>
            <p><strong>Diễn giả:</strong> {{webinar_speaker}}</p>
            <p><strong>Ngày đăng ký:</strong> {{registration_date}}</p>
            
            <p>Chúng tôi rất vui mừng được chào đón bạn tham gia webinar này. Đây sẽ là một cơ hội tuyệt vời để bạn học hỏi và kết nối với cộng đồng.</p>
            
            <div style="text-align: center;">
                <a href="{{join_url}}" class="button">🚀 Tham Gia Webinar</a>
            </div>
            
            <h3>📋 Những điều cần chuẩn bị:</h3>
            <ul>
                <li>Kết nối internet ổn định</li>
                <li>Thiết bị có camera và microphone (nếu muốn tương tác)</li>
                <li>Sổ tay để ghi chú</li>
                <li>Tinh thần học hỏi tích cực</li>
            </ul>
            
            <p>Nếu bạn có bất kỳ câu hỏi nào, đừng ngần ngại liên hệ với chúng tôi.</p>
        </div>
        <div class="footer">
            <p>Email này được gửi từ {{site_name}}<br>
            <a href="{{unsubscribe_url}}">Hủy đăng ký nhận email</a></p>
        </div>
    </div>
</body>
</html>';
    }

    private function getReminderEmailContent()
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nhắc nhở webinar</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(45deg, #f6c23e, #dda20a); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f8f9fc; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #e74a3b; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .countdown { background: #fff; border: 2px solid #f6c23e; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⏰ Nhắc nhở quan trọng!</h1>
            <p>Webinar của bạn sắp bắt đầu</p>
        </div>
        <div class="content">
            <h2>{{webinar_title}}</h2>
            <p>Xin chào {{participant_name}},</p>
            
            <div class="countdown">
                <h3>🕒 Thời gian còn lại: {{time_until_webinar}}</h3>
                <p><strong>Ngày giờ:</strong> {{webinar_date}}</p>
                <p><strong>Diễn giả:</strong> {{webinar_speaker}}</p>
            </div>
            
            <p>Đừng quên tham gia webinar mà bạn đã đăng ký! Chúng tôi đã chuẩn bị rất nhiều nội dung hữu ích và thú vị cho bạn.</p>
            
            <div style="text-align: center;">
                <a href="{{join_url}}" class="button">🎯 Tham Gia Ngay</a>
            </div>
            
            <h3>💡 Lời khuyên:</h3>
            <ul>
                <li>Tham gia sớm 5-10 phút để kiểm tra kết nối</li>
                <li>Chuẩn bị câu hỏi để tương tác với diễn giả</li>
                <li>Tắt thông báo để tập trung hoàn toàn</li>
            </ul>
            
            <p>Hẹn gặp bạn trong webinar!</p>
        </div>
        <div class="footer">
            <p>Email này được gửi từ {{site_name}}<br>
            <a href="{{unsubscribe_url}}">Hủy đăng ký nhận email</a></p>
        </div>
    </div>
</body>
</html>';
    }

    private function getFollowUpEmailContent()
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cảm ơn bạn đã tham gia</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(45deg, #1cc88a, #17a673); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f8f9fc; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #4e73df; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
        .resources { background: #fff; border-left: 4px solid #1cc88a; padding: 20px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🙏 Cảm ơn {{participant_name}}!</h1>
            <p>Cảm ơn bạn đã tham gia webinar của chúng tôi</p>
        </div>
        <div class="content">
            <h2>{{webinar_title}}</h2>
            
            <p>Chúng tôi hy vọng bạn đã có những trải nghiệm thú vị và học được nhiều điều bổ ích từ webinar vừa qua.</p>
            
            <div class="resources">
                <h3>📚 Tài liệu và ghi chú:</h3>
                <p>Chúng tôi đã chuẩn bị một số tài liệu hữu ích cho bạn:</p>
                <div style="text-align: center;">
                    <a href="{{recording_url}}" class="button">📹 Xem lại Recording</a>
                    <a href="{{materials_url}}" class="button">📄 Tải tài liệu</a>
                </div>
            </div>
            
            <h3>🚀 Bước tiếp theo:</h3>
            <ul>
                <li>Xem lại recording để củng cố kiến thức</li>
                <li>Thực hành những gì đã học</li>
                <li>Chia sẻ với bạn bè nếu thấy hữu ích</li>
                <li>Theo dõi chúng tôi để nhận thông tin về các webinar tiếp theo</li>
            </ul>
            
            <p>Nếu bạn có bất kỳ câu hỏi nào hoặc cần hỗ trợ thêm, đừng ngần ngại liên hệ với chúng tôi.</p>
            
            <p>Một lần nữa, cảm ơn bạn đã dành thời gian tham gia!</p>
        </div>
        <div class="footer">
            <p>Email này được gửi từ {{site_name}}<br>
            <a href="{{unsubscribe_url}}">Hủy đăng ký nhận email</a></p>
        </div>
    </div>
</body>
</html>';
    }

    private function getPromotionalEmailContent()
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ưu đãi đặc biệt</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(45deg, #e74a3b, #c0392b); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f8f9fc; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #f39c12; color: white; padding: 15px 40px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-size: 18px; font-weight: bold; }
        .offer { background: #fff; border: 3px solid #e74a3b; padding: 25px; border-radius: 10px; text-align: center; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎁 Ưu đãi đặc biệt!</h1>
            <p>Dành riêng cho {{participant_name}}</p>
        </div>
        <div class="content">
            <p>Xin chào {{participant_name}},</p>
            
            <p>Cảm ơn bạn đã quan tâm và tham gia các hoạt động của {{site_name}}. Chúng tôi có một ưu đãi đặc biệt dành riêng cho bạn!</p>
            
            <div class="offer">
                <h2>🔥 GIẢM GIÁ 50%</h2>
                <p style="font-size: 18px;">Cho tất cả khóa học và webinar premium</p>
                <p style="color: #e74a3b; font-weight: bold;">Chỉ còn 3 ngày!</p>
            </div>
            
            <h3>✨ Bạn sẽ nhận được:</h3>
            <ul>
                <li>Truy cập không giới hạn tất cả khóa học</li>
                <li>Tài liệu độc quyền và bài tập thực hành</li>
                <li>Hỗ trợ 1-1 từ chuyên gia</li>
                <li>Chứng chỉ hoàn thành khóa học</li>
                <li>Cộng đồng học viên riêng biệt</li>
            </ul>
            
            <div style="text-align: center;">
                <a href="{{site_url}}" class="button">🛒 Nhận Ưu Đãi Ngay</a>
            </div>
            
            <p style="color: #e74a3b; font-weight: bold; text-align: center;">
                ⏰ Ưu đãi có hạn - Đừng bỏ lỡ cơ hội này!
            </p>
            
            <p>Nếu bạn có bất kỳ câu hỏi nào, đội ngũ hỗ trợ của chúng tôi luôn sẵn sàng giúp đỡ.</p>
        </div>
        <div class="footer">
            <p>Email này được gửi từ {{site_name}}<br>
            <a href="{{unsubscribe_url}}">Hủy đăng ký nhận email</a></p>
        </div>
    </div>
</body>
</html>';
    }

    private function getDefaultVariables()
    {
        return [
            '{{site_name}}' => 'Tên website',
            '{{site_url}}' => 'URL website',
            '{{participant_name}}' => 'Tên người tham gia',
            '{{participant_email}}' => 'Email người tham gia',
            '{{webinar_title}}' => 'Tiêu đề webinar',
            '{{webinar_speaker}}' => 'Diễn giả',
            '{{join_url}}' => 'Link tham gia webinar',
            '{{registration_date}}' => 'Ngày đăng ký',
            '{{webinar_date}}' => 'Ngày diễn ra webinar',
            '{{time_until_webinar}}' => 'Thời gian còn lại',
            '{{recording_url}}' => 'Link xem lại',
            '{{materials_url}}' => 'Link tài liệu',
            '{{unsubscribe_url}}' => 'Link hủy đăng ký',
        ];
    }
}
