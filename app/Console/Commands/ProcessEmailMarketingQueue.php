<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\EmailLog;
use App\Jobs\SendEmailMarketingJob;
use Illuminate\Support\Facades\Log;

class ProcessEmailMarketingQueue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email-marketing:process-queue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending email marketing queue and send scheduled emails';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Processing email marketing queue...');

        // Get emails that are scheduled to be sent
        $pendingEmails = EmailLog::scheduledForSending()
            ->with(['workflow.emailTemplate', 'participant', 'campaign.webinar'])
            ->limit(100) // Process in batches
            ->get();

        if ($pendingEmails->isEmpty()) {
            $this->info('No emails to process.');
            return 0;
        }

        $this->info("Found {$pendingEmails->count()} emails to process.");

        $processed = 0;
        $failed = 0;

        foreach ($pendingEmails as $emailLog) {
            try {
                // Dispatch job to send email
                SendEmailMarketingJob::dispatch($emailLog);
                $processed++;
                
                $this->line("Dispatched email job for: {$emailLog->recipient_email}");
                
            } catch (\Exception $e) {
                $failed++;
                $emailLog->markAsFailed($e->getMessage());
                
                $this->error("Failed to dispatch email for {$emailLog->recipient_email}: {$e->getMessage()}");
                Log::error("Email marketing queue processing failed", [
                    'email_log_id' => $emailLog->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->info("Processing completed. Processed: {$processed}, Failed: {$failed}");
        
        Log::info("Email marketing queue processed", [
            'processed' => $processed,
            'failed' => $failed,
            'total' => $pendingEmails->count()
        ]);

        return 0;
    }
}
