<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\EmailLog;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class CleanupEmailLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email-marketing:cleanup-logs {--days=90 : Number of days to keep logs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup old email marketing logs to save database space';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        
        if ($days < 1) {
            $this->error('Days must be a positive number.');
            return 1;
        }

        $this->info("Cleaning up email logs older than {$days} days...");

        $cutoffDate = Carbon::now()->subDays($days);
        
        // Count logs to be deleted
        $logsToDelete = EmailLog::where('created_at', '<', $cutoffDate)->count();
        
        if ($logsToDelete === 0) {
            $this->info('No old logs found to cleanup.');
            return 0;
        }

        if ($this->confirm("This will delete {$logsToDelete} email logs. Continue?")) {
            try {
                // Delete old logs in batches to avoid memory issues
                $batchSize = 1000;
                $totalDeleted = 0;
                
                do {
                    $deleted = EmailLog::where('created_at', '<', $cutoffDate)
                        ->limit($batchSize)
                        ->delete();
                    
                    $totalDeleted += $deleted;
                    
                    if ($deleted > 0) {
                        $this->line("Deleted {$deleted} logs (Total: {$totalDeleted})");
                    }
                    
                } while ($deleted > 0);

                $this->info("Cleanup completed. Deleted {$totalDeleted} email logs.");
                
                Log::info("Email logs cleanup completed", [
                    'deleted_count' => $totalDeleted,
                    'cutoff_date' => $cutoffDate->toDateString(),
                    'retention_days' => $days
                ]);

            } catch (\Exception $e) {
                $this->error("Cleanup failed: {$e->getMessage()}");
                Log::error("Email logs cleanup failed", [
                    'error' => $e->getMessage(),
                    'cutoff_date' => $cutoffDate->toDateString()
                ]);
                return 1;
            }
        } else {
            $this->info('Cleanup cancelled.');
        }

        return 0;
    }
}
