<?php

namespace App\Events;

use App\Models\WebinarParticipant;
use App\Models\Webinar;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WebinarParticipantRegisteredForEmail
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $participant;
    public $webinar;

    /**
     * Create a new event instance.
     */
    public function __construct(WebinarParticipant $participant, Webinar $webinar)
    {
        $this->participant = $participant;
        $this->webinar = $webinar;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
