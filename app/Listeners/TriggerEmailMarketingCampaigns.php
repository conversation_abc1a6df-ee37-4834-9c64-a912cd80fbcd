<?php

namespace App\Listeners;

use App\Events\WebinarParticipantRegisteredForEmail;
use App\Models\EmailCampaign;
use App\Models\EmailLog;
use App\Jobs\SendEmailMarketingJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class TriggerEmailMarketingCampaigns implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(WebinarParticipantRegisteredForEmail $event): void
    {
        $participant = $event->participant;
        $webinar = $event->webinar;

        Log::info("Triggering email marketing campaigns for participant {$participant->id} in webinar {$webinar->id}");

        // Find active campaigns for this webinar or global campaigns
        $campaigns = EmailCampaign::active()
            ->where(function ($query) use ($webinar) {
                $query->where('webinar_id', $webinar->id)
                      ->orWhereNull('webinar_id');
            })
            ->where('trigger_type', 'registration')
            ->with(['workflows.emailTemplate'])
            ->get();

        foreach ($campaigns as $campaign) {
            $this->processCampaignForParticipant($campaign, $participant, $webinar);
        }
    }

    /**
     * Process a campaign for a specific participant.
     */
    private function processCampaignForParticipant($campaign, $participant, $webinar)
    {
        Log::info("Processing campaign {$campaign->id} for participant {$participant->id}");

        // Get active workflows for this campaign
        $workflows = $campaign->workflows()->active()->orderBy('sequence_order')->get();

        foreach ($workflows as $workflow) {
            // Check if workflow conditions are met
            if (!$workflow->checkConditions($participant, $webinar)) {
                Log::info("Workflow {$workflow->id} conditions not met for participant {$participant->id}");
                continue;
            }

            // Calculate scheduled time
            $scheduledAt = $workflow->calculateScheduledTime(now());

            // Render email content for logging
            $template = $workflow->emailTemplate;
            $variables = $this->prepareEmailVariables($participant, $webinar);
            $rendered = $template->renderContent($variables);

            // Create email log
            $emailLog = EmailLog::create([
                'campaign_id' => $campaign->id,
                'workflow_id' => $workflow->id,
                'webinar_participant_id' => $participant->id,
                'recipient_email' => $participant->email,
                'recipient_name' => $participant->name,
                'subject' => $rendered['subject'],
                'content' => $rendered['content'],
                'status' => 'pending',
                'scheduled_at' => $scheduledAt,
            ]);

            // Schedule email job
            if ($scheduledAt->isPast()) {
                // Send immediately
                SendEmailMarketingJob::dispatch($emailLog);
            } else {
                // Schedule for later
                SendEmailMarketingJob::dispatch($emailLog)->delay($scheduledAt);
            }

            Log::info("Email scheduled for participant {$participant->id}, workflow {$workflow->id}, scheduled at {$scheduledAt}");
        }
    }

    /**
     * Prepare variables for email template.
     */
    private function prepareEmailVariables($participant, $webinar)
    {
        $variables = [
            '{{participant_name}}' => $participant->name ?? '',
            '{{participant_email}}' => $participant->email ?? '',
            '{{participant_phone}}' => $participant->phone ?? '',
            '{{webinar_title}}' => $webinar->title ?? '',
            '{{webinar_speaker}}' => $webinar->speaker ?? '',
            '{{join_url}}' => $webinar->join_url ?? '',
            '{{registration_date}}' => $participant->created_at ? $participant->created_at->format('d/m/Y H:i') : '',
        ];

        // Add schedule-related variables
        if ($webinar->schedules && is_array($webinar->schedules)) {
            $nextSchedule = collect($webinar->schedules)
                ->where('date', '>=', now()->format('Y-m-d'))
                ->first();
            
            if ($nextSchedule) {
                $variables['{{webinar_date}}'] = \Carbon\Carbon::parse($nextSchedule['date'] . ' ' . $nextSchedule['time'])->format('d/m/Y H:i');
                $variables['{{time_until_webinar}}'] = \Carbon\Carbon::parse($nextSchedule['date'] . ' ' . $nextSchedule['time'])->diffForHumans();
            }
        }

        return $variables;
    }

    /**
     * Handle a job failure.
     */
    public function failed(WebinarParticipantRegisteredForEmail $event, \Throwable $exception): void
    {
        Log::error("Failed to trigger email marketing campaigns: " . $exception->getMessage());
    }
}
