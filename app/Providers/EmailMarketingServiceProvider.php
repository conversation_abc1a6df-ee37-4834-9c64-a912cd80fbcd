<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Events\WebinarParticipantRegisteredForEmail;
use App\Listeners\TriggerEmailMarketingCampaigns;

class EmailMarketingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register event listener for email marketing campaigns
        Event::listen(
            WebinarParticipantRegisteredForEmail::class,
            TriggerEmailMarketingCampaigns::class
        );
    }
}
