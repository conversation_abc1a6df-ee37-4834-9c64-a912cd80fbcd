<?php

namespace App\Providers;

use App\Models\Advertisement;
use App\Models\Product;
use App\Models\SmartLink;
use App\Models\Webinar;
use App\Policies\AdvertisementPolicy;
use App\Policies\ProductPolicy;
use App\Policies\SmartLinkPolicy;
use App\Policies\WebinarPolicy;
use App\Models\EmailCampaign;
use App\Models\EmailTemplate;
use App\Policies\EmailCampaignPolicy;
use App\Policies\EmailTemplatePolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Webinar::class => WebinarPolicy::class,
        Product::class => ProductPolicy::class,
        Advertisement::class => AdvertisementPolicy::class,
        SmartLink::class => SmartLinkPolicy::class,
        EmailCampaign::class => EmailCampaignPolicy::class,
        EmailTemplate::class => EmailTemplatePolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();
    }
}
