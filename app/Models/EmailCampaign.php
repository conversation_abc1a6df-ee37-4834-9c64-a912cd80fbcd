<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmailCampaign extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'webinar_id',
        'name',
        'description',
        'status',
        'trigger_type',
        'trigger_conditions',
        'is_active',
        'started_at',
        'completed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'trigger_conditions' => 'array',
        'is_active' => 'boolean',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the campaign.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the webinar associated with the campaign.
     */
    public function webinar()
    {
        return $this->belongsTo(Webinar::class);
    }

    /**
     * Get the email templates for the campaign.
     */
    public function emailTemplates()
    {
        return $this->hasMany(EmailTemplate::class, 'campaign_id');
    }

    /**
     * Get the workflows for the campaign.
     */
    public function workflows()
    {
        return $this->hasMany(EmailWorkflow::class, 'campaign_id')->orderBy('sequence_order');
    }

    /**
     * Get the email logs for the campaign.
     */
    public function emailLogs()
    {
        return $this->hasMany(EmailLog::class, 'campaign_id');
    }

    /**
     * Scope for active campaigns.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')->where('is_active', true);
    }

    /**
     * Scope for campaigns by trigger type.
     */
    public function scopeByTriggerType($query, $triggerType)
    {
        return $query->where('trigger_type', $triggerType);
    }

    /**
     * Get campaign statistics.
     */
    public function getStatsAttribute()
    {
        $logs = $this->emailLogs();
        
        return [
            'total_sent' => $logs->where('status', 'sent')->count(),
            'total_delivered' => $logs->where('status', 'delivered')->count(),
            'total_opened' => $logs->where('status', 'opened')->count(),
            'total_clicked' => $logs->where('status', 'clicked')->count(),
            'total_failed' => $logs->where('status', 'failed')->count(),
            'open_rate' => $this->calculateOpenRate(),
            'click_rate' => $this->calculateClickRate(),
        ];
    }

    /**
     * Calculate open rate.
     */
    private function calculateOpenRate()
    {
        $delivered = $this->emailLogs()->where('status', 'delivered')->count();
        $opened = $this->emailLogs()->where('status', 'opened')->count();
        
        return $delivered > 0 ? round(($opened / $delivered) * 100, 2) : 0;
    }

    /**
     * Calculate click rate.
     */
    private function calculateClickRate()
    {
        $delivered = $this->emailLogs()->where('status', 'delivered')->count();
        $clicked = $this->emailLogs()->where('status', 'clicked')->count();
        
        return $delivered > 0 ? round(($clicked / $delivered) * 100, 2) : 0;
    }
}
