<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'campaign_id',
        'workflow_id',
        'webinar_participant_id',
        'recipient_email',
        'recipient_name',
        'subject',
        'content',
        'status',
        'error_message',
        'scheduled_at',
        'sent_at',
        'delivered_at',
        'opened_at',
        'clicked_at',
        'open_count',
        'click_count',
        'tracking_data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'tracking_data' => 'array',
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'opened_at' => 'datetime',
        'clicked_at' => 'datetime',
        'open_count' => 'integer',
        'click_count' => 'integer',
    ];

    /**
     * Get the campaign that owns the log.
     */
    public function campaign()
    {
        return $this->belongsTo(EmailCampaign::class, 'campaign_id');
    }

    /**
     * Get the workflow that owns the log.
     */
    public function workflow()
    {
        return $this->belongsTo(EmailWorkflow::class, 'workflow_id');
    }

    /**
     * Get the participant associated with the log.
     */
    public function participant()
    {
        return $this->belongsTo(WebinarParticipant::class, 'webinar_participant_id');
    }

    /**
     * Scope for pending emails.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for sent emails.
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    /**
     * Scope for failed emails.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for opened emails.
     */
    public function scopeOpened($query)
    {
        return $query->where('status', 'opened');
    }

    /**
     * Scope for clicked emails.
     */
    public function scopeClicked($query)
    {
        return $query->where('status', 'clicked');
    }

    /**
     * Scope for emails scheduled to be sent.
     */
    public function scopeScheduledForSending($query)
    {
        return $query->where('status', 'pending')
                    ->where('scheduled_at', '<=', now());
    }

    /**
     * Mark email as sent.
     */
    public function markAsSent()
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    /**
     * Mark email as failed.
     */
    public function markAsFailed($errorMessage = null)
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Mark email as delivered.
     */
    public function markAsDelivered()
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    /**
     * Mark email as opened.
     */
    public function markAsOpened()
    {
        $this->update([
            'status' => 'opened',
            'opened_at' => $this->opened_at ?: now(),
            'open_count' => $this->open_count + 1,
        ]);
    }

    /**
     * Mark email as clicked.
     */
    public function markAsClicked()
    {
        $this->update([
            'status' => 'clicked',
            'clicked_at' => $this->clicked_at ?: now(),
            'click_count' => $this->click_count + 1,
        ]);
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColor()
    {
        return match($this->status) {
            'pending' => 'warning',
            'sent' => 'info',
            'delivered' => 'success',
            'opened' => 'primary',
            'clicked' => 'success',
            'failed' => 'danger',
            'bounced' => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Get status text in Vietnamese.
     */
    public function getStatusText()
    {
        return match($this->status) {
            'pending' => 'Chờ gửi',
            'sent' => 'Đã gửi',
            'delivered' => 'Đã nhận',
            'opened' => 'Đã mở',
            'clicked' => 'Đã click',
            'failed' => 'Thất bại',
            'bounced' => 'Bị trả về',
            default => 'Không xác định',
        };
    }
}
