<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class EmailWorkflow extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'campaign_id',
        'email_template_id',
        'sequence_order',
        'delay_days',
        'delay_hours',
        'delay_minutes',
        'conditions',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'conditions' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the campaign that owns the workflow.
     */
    public function campaign()
    {
        return $this->belongsTo(EmailCampaign::class, 'campaign_id');
    }

    /**
     * Get the email template for this workflow.
     */
    public function emailTemplate()
    {
        return $this->belongsTo(EmailTemplate::class, 'email_template_id');
    }

    /**
     * Get the email logs for this workflow.
     */
    public function emailLogs()
    {
        return $this->hasMany(EmailLog::class, 'workflow_id');
    }

    /**
     * Scope for active workflows.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Calculate the scheduled time for sending email.
     */
    public function calculateScheduledTime($triggerTime)
    {
        $scheduledTime = Carbon::parse($triggerTime);
        
        if ($this->delay_days > 0) {
            $scheduledTime->addDays($this->delay_days);
        }
        
        if ($this->delay_hours > 0) {
            $scheduledTime->addHours($this->delay_hours);
        }
        
        if ($this->delay_minutes > 0) {
            $scheduledTime->addMinutes($this->delay_minutes);
        }

        return $scheduledTime;
    }

    /**
     * Check if conditions are met for sending email.
     */
    public function checkConditions($participant, $webinar = null)
    {
        if (empty($this->conditions)) {
            return true;
        }

        foreach ($this->conditions as $condition) {
            $field = $condition['field'] ?? null;
            $operator = $condition['operator'] ?? 'equals';
            $value = $condition['value'] ?? null;

            if (!$this->evaluateCondition($participant, $webinar, $field, $operator, $value)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Evaluate a single condition.
     */
    private function evaluateCondition($participant, $webinar, $field, $operator, $value)
    {
        $fieldValue = $this->getFieldValue($participant, $webinar, $field);

        switch ($operator) {
            case 'equals':
                return $fieldValue == $value;
            case 'not_equals':
                return $fieldValue != $value;
            case 'contains':
                return strpos($fieldValue, $value) !== false;
            case 'not_contains':
                return strpos($fieldValue, $value) === false;
            case 'greater_than':
                return $fieldValue > $value;
            case 'less_than':
                return $fieldValue < $value;
            case 'is_empty':
                return empty($fieldValue);
            case 'is_not_empty':
                return !empty($fieldValue);
            default:
                return true;
        }
    }

    /**
     * Get field value from participant or webinar.
     */
    private function getFieldValue($participant, $webinar, $field)
    {
        switch ($field) {
            case 'participant_name':
                return $participant->name ?? '';
            case 'participant_email':
                return $participant->email ?? '';
            case 'participant_phone':
                return $participant->phone ?? '';
            case 'join_count':
                return $participant->join_count ?? 0;
            case 'view_duration':
                return $participant->view_duration ?? 0;
            case 'webinar_title':
                return $webinar->title ?? '';
            case 'webinar_speaker':
                return $webinar->speaker ?? '';
            default:
                return '';
        }
    }

    /**
     * Get total delay in minutes.
     */
    public function getTotalDelayMinutes()
    {
        return ($this->delay_days * 24 * 60) + ($this->delay_hours * 60) + $this->delay_minutes;
    }

    /**
     * Get human readable delay.
     */
    public function getDelayText()
    {
        $parts = [];
        
        if ($this->delay_days > 0) {
            $parts[] = $this->delay_days . ' ngày';
        }
        
        if ($this->delay_hours > 0) {
            $parts[] = $this->delay_hours . ' giờ';
        }
        
        if ($this->delay_minutes > 0) {
            $parts[] = $this->delay_minutes . ' phút';
        }

        return empty($parts) ? 'Ngay lập tức' : implode(', ', $parts);
    }
}
