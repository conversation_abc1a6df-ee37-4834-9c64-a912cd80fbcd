<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmailTemplate extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'campaign_id',
        'name',
        'subject',
        'content',
        'template_type',
        'design_json',
        'variables',
        'is_default',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'design_json' => 'array',
        'variables' => 'array',
        'is_default' => 'boolean',
    ];

    /**
     * Get the user that owns the template.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the campaign that owns the template.
     */
    public function campaign()
    {
        return $this->belongsTo(EmailCampaign::class, 'campaign_id');
    }

    /**
     * Get the workflows using this template.
     */
    public function workflows()
    {
        return $this->hasMany(EmailWorkflow::class, 'email_template_id');
    }

    /**
     * Scope for default templates.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope for templates by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('template_type', $type);
    }

    /**
     * Replace variables in content with actual values.
     */
    public function renderContent($variables = [])
    {
        $content = $this->content;
        $subject = $this->subject;

        // Default variables
        $defaultVariables = [
            '{{site_name}}' => config('app.name'),
            '{{site_url}}' => config('app.url'),
            '{{current_year}}' => date('Y'),
            '{{current_date}}' => date('d/m/Y'),
        ];

        // Merge with provided variables
        $allVariables = array_merge($defaultVariables, $variables);

        // Replace variables in content and subject
        foreach ($allVariables as $key => $value) {
            $content = str_replace($key, $value, $content);
            $subject = str_replace($key, $value, $subject);
        }

        return [
            'subject' => $subject,
            'content' => $content,
        ];
    }

    /**
     * Get available variables for this template type.
     */
    public function getAvailableVariables()
    {
        $baseVariables = [
            '{{site_name}}' => 'Tên website',
            '{{site_url}}' => 'URL website',
            '{{current_year}}' => 'Năm hiện tại',
            '{{current_date}}' => 'Ngày hiện tại',
            '{{participant_name}}' => 'Tên người tham gia',
            '{{participant_email}}' => 'Email người tham gia',
            '{{webinar_title}}' => 'Tiêu đề webinar',
            '{{webinar_speaker}}' => 'Diễn giả',
            '{{join_url}}' => 'Link tham gia webinar',
        ];

        // Add specific variables based on template type
        switch ($this->template_type) {
            case 'welcome':
                $baseVariables['{{registration_date}}'] = 'Ngày đăng ký';
                break;
            case 'reminder':
                $baseVariables['{{webinar_date}}'] = 'Ngày diễn ra webinar';
                $baseVariables['{{time_until_webinar}}'] = 'Thời gian còn lại';
                break;
            case 'follow_up':
                $baseVariables['{{recording_url}}'] = 'Link xem lại';
                $baseVariables['{{materials_url}}'] = 'Link tài liệu';
                break;
        }

        return $baseVariables;
    }
}
