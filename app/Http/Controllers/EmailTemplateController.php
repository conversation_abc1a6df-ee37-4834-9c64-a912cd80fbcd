<?php

namespace App\Http\Controllers;

use App\Models\EmailTemplate;
use App\Models\EmailCampaign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use RealRashid\SweetAlert\Facades\Alert;

class EmailTemplateController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->can('email marketing')) {
                abort(403, 'Bạn không có quyền truy cập tính năng này.');
            }
            return $next($request);
        });
    }

    /**
     * Display templates list.
     */
    public function index()
    {
        $user = Auth::user();
        $templates = EmailTemplate::where('user_id', $user->id)
            ->with(['campaign'])
            ->latest()
            ->paginate(15);

        return view('email-marketing.templates.index', compact('templates'));
    }

    /**
     * Show the form for creating a new template.
     */
    public function create()
    {
        $user = Auth::user();
        $campaigns = EmailCampaign::where('user_id', $user->id)->get();
        
        return view('email-marketing.templates.create', compact('campaigns'));
    }

    /**
     * Store a newly created template.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'template_type' => 'required|in:welcome,reminder,follow_up,promotional,custom',
            'campaign_id' => 'nullable|exists:email_campaigns,id',
            'variables' => 'nullable|array',
        ]);

        $template = EmailTemplate::create([
            'user_id' => Auth::id(),
            'name' => $request->name,
            'subject' => $request->subject,
            'content' => $request->content,
            'template_type' => $request->template_type,
            'campaign_id' => $request->campaign_id,
            'variables' => $request->variables,
        ]);

        Alert::success('Thành công', 'Đã tạo template email thành công!');
        return redirect()->route('email-marketing.templates.show', $template);
    }

    /**
     * Display the specified template.
     */
    public function show(EmailTemplate $template)
    {
        $this->authorize('view', $template);
        
        $template->load(['campaign', 'workflows']);
        
        return view('email-marketing.templates.show', compact('template'));
    }

    /**
     * Show the form for editing the specified template.
     */
    public function edit(EmailTemplate $template)
    {
        $this->authorize('update', $template);
        
        $user = Auth::user();
        $campaigns = EmailCampaign::where('user_id', $user->id)->get();
        
        return view('email-marketing.templates.edit', compact('template', 'campaigns'));
    }

    /**
     * Update the specified template.
     */
    public function update(Request $request, EmailTemplate $template)
    {
        $this->authorize('update', $template);
        
        $request->validate([
            'name' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'template_type' => 'required|in:welcome,reminder,follow_up,promotional,custom',
            'campaign_id' => 'nullable|exists:email_campaigns,id',
            'variables' => 'nullable|array',
        ]);

        $template->update([
            'name' => $request->name,
            'subject' => $request->subject,
            'content' => $request->content,
            'template_type' => $request->template_type,
            'campaign_id' => $request->campaign_id,
            'variables' => $request->variables,
        ]);

        Alert::success('Thành công', 'Đã cập nhật template thành công!');
        return redirect()->route('email-marketing.templates.show', $template);
    }

    /**
     * Remove the specified template.
     */
    public function destroy(EmailTemplate $template)
    {
        $this->authorize('delete', $template);
        
        // Check if template is being used in workflows
        if ($template->workflows()->count() > 0) {
            Alert::error('Lỗi', 'Không thể xóa template đang được sử dụng trong workflow!');
            return back();
        }
        
        $template->delete();
        
        Alert::success('Thành công', 'Đã xóa template thành công!');
        return redirect()->route('email-marketing.templates');
    }

    /**
     * Preview template with sample data.
     */
    public function preview(EmailTemplate $template)
    {
        $this->authorize('view', $template);
        
        // Sample variables for preview
        $sampleVariables = [
            '{{participant_name}}' => 'Nguyễn Văn A',
            '{{participant_email}}' => '<EMAIL>',
            '{{webinar_title}}' => 'Webinar Mẫu - Học Marketing Online',
            '{{webinar_speaker}}' => 'Chuyên gia ABC',
            '{{join_url}}' => 'https://webinar.test/join/sample123',
            '{{registration_date}}' => '15/06/2025',
            '{{webinar_date}}' => '20/06/2025',
            '{{time_until_webinar}}' => '2 ngày',
            '{{recording_url}}' => 'https://webinar.test/recording/sample',
            '{{materials_url}}' => 'https://webinar.test/materials/sample',
        ];

        $rendered = $template->renderContent($sampleVariables);
        
        return view('email-marketing.templates.preview', compact('template', 'rendered'));
    }

    /**
     * Duplicate template.
     */
    public function duplicate(EmailTemplate $template)
    {
        $this->authorize('view', $template);
        
        $newTemplate = $template->replicate();
        $newTemplate->name = $template->name . ' (Copy)';
        $newTemplate->campaign_id = null; // Remove campaign association
        $newTemplate->save();
        
        Alert::success('Thành công', 'Đã sao chép template thành công!');
        return redirect()->route('email-marketing.templates.edit', $newTemplate);
    }

    /**
     * Get available variables for template type.
     */
    public function getVariables(Request $request)
    {
        $templateType = $request->get('type', 'custom');
        
        $template = new EmailTemplate(['template_type' => $templateType]);
        $variables = $template->getAvailableVariables();
        
        return response()->json($variables);
    }
}
