<?php

namespace App\Http\Controllers;

use App\Models\EmailCampaign;
use App\Models\EmailTemplate;
use App\Models\EmailWorkflow;
use App\Models\EmailLog;
use App\Models\Webinar;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use RealRashid\SweetAlert\Facades\Alert;

class EmailMarketingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->can('email marketing')) {
                abort(403, 'Bạn không có quyền truy cập tính năng này.');
            }
            return $next($request);
        });
    }

    /**
     * Display email marketing dashboard.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get campaigns statistics
        $totalCampaigns = EmailCampaign::where('user_id', $user->id)->count();
        $activeCampaigns = EmailCampaign::where('user_id', $user->id)->where('status', 'active')->count();
        $totalEmailsSent = EmailLog::whereHas('campaign', function($query) use ($user) {
            $query->where('user_id', $user->id);
        })->where('status', 'sent')->count();
        
        $totalEmailsOpened = EmailLog::whereHas('campaign', function($query) use ($user) {
            $query->where('user_id', $user->id);
        })->where('status', 'opened')->count();

        // Calculate open rate
        $openRate = $totalEmailsSent > 0 ? round(($totalEmailsOpened / $totalEmailsSent) * 100, 2) : 0;

        // Get recent campaigns
        $recentCampaigns = EmailCampaign::where('user_id', $user->id)
            ->with(['webinar', 'emailLogs'])
            ->latest()
            ->take(5)
            ->get();

        // Get recent email logs
        $recentLogs = EmailLog::whereHas('campaign', function($query) use ($user) {
            $query->where('user_id', $user->id);
        })->with(['campaign', 'participant'])
            ->latest()
            ->take(10)
            ->get();

        return view('email-marketing.index', compact(
            'totalCampaigns',
            'activeCampaigns', 
            'totalEmailsSent',
            'totalEmailsOpened',
            'openRate',
            'recentCampaigns',
            'recentLogs'
        ));
    }

    /**
     * Display campaigns list.
     */
    public function campaigns()
    {
        $user = Auth::user();
        $campaigns = EmailCampaign::where('user_id', $user->id)
            ->with(['webinar', 'workflows', 'emailLogs'])
            ->latest()
            ->paginate(15);

        return view('email-marketing.campaigns.index', compact('campaigns'));
    }

    /**
     * Show the form for creating a new campaign.
     */
    public function createCampaign()
    {
        $user = Auth::user();
        $webinars = Webinar::where('user_id', $user->id)->get();
        
        return view('email-marketing.campaigns.create', compact('webinars'));
    }

    /**
     * Store a newly created campaign.
     */
    public function storeCampaign(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'webinar_id' => 'nullable|exists:webinars,id',
            'trigger_type' => 'required|in:registration,before_webinar,after_webinar,custom',
            'trigger_conditions' => 'nullable|array',
        ]);

        $campaign = EmailCampaign::create([
            'user_id' => Auth::id(),
            'name' => $request->name,
            'description' => $request->description,
            'webinar_id' => $request->webinar_id,
            'trigger_type' => $request->trigger_type,
            'trigger_conditions' => $request->trigger_conditions,
            'status' => 'draft',
        ]);

        Alert::success('Thành công', 'Đã tạo campaign email marketing thành công!');
        return redirect()->route('email-marketing.campaigns.show', $campaign);
    }

    /**
     * Display the specified campaign.
     */
    public function showCampaign(EmailCampaign $campaign)
    {
        $this->authorize('view', $campaign);
        
        $campaign->load(['webinar', 'workflows.emailTemplate', 'emailLogs']);
        
        // Get campaign statistics
        $stats = $campaign->stats;
        
        return view('email-marketing.campaigns.show', compact('campaign', 'stats'));
    }

    /**
     * Show the form for editing the specified campaign.
     */
    public function editCampaign(EmailCampaign $campaign)
    {
        $this->authorize('update', $campaign);
        
        $user = Auth::user();
        $webinars = Webinar::where('user_id', $user->id)->get();
        
        return view('email-marketing.campaigns.edit', compact('campaign', 'webinars'));
    }

    /**
     * Update the specified campaign.
     */
    public function updateCampaign(Request $request, EmailCampaign $campaign)
    {
        $this->authorize('update', $campaign);
        
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'webinar_id' => 'nullable|exists:webinars,id',
            'trigger_type' => 'required|in:registration,before_webinar,after_webinar,custom',
            'trigger_conditions' => 'nullable|array',
        ]);

        $campaign->update([
            'name' => $request->name,
            'description' => $request->description,
            'webinar_id' => $request->webinar_id,
            'trigger_type' => $request->trigger_type,
            'trigger_conditions' => $request->trigger_conditions,
        ]);

        Alert::success('Thành công', 'Đã cập nhật campaign thành công!');
        return redirect()->route('email-marketing.campaigns.show', $campaign);
    }

    /**
     * Remove the specified campaign.
     */
    public function destroyCampaign(EmailCampaign $campaign)
    {
        $this->authorize('delete', $campaign);
        
        $campaign->delete();
        
        Alert::success('Thành công', 'Đã xóa campaign thành công!');
        return redirect()->route('email-marketing.campaigns');
    }

    /**
     * Toggle campaign status.
     */
    public function toggleCampaignStatus(EmailCampaign $campaign)
    {
        $this->authorize('update', $campaign);
        
        $newStatus = $campaign->status === 'active' ? 'paused' : 'active';
        
        $campaign->update([
            'status' => $newStatus,
            'started_at' => $newStatus === 'active' ? now() : $campaign->started_at,
        ]);

        $message = $newStatus === 'active' ? 'Đã kích hoạt campaign!' : 'Đã tạm dừng campaign!';
        Alert::success('Thành công', $message);
        
        return back();
    }
}
