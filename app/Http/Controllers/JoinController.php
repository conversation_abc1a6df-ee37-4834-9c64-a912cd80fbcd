<?php

namespace App\Http\Controllers;

use App\Models\Advertisement;
use App\Models\Student;
use App\Models\Webinar;
use App\Models\WebinarComment;
use App\Models\WebinarParticipant;
use Illuminate\Http\Request;
use Carbon\Carbon;
use App\Events\WebinarParticipantRegistered;
use App\Events\WebinarParticipantRegisteredForEmail;
use App\Events\WebinarCommentSubmitted;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use RealRashid\SweetAlert\Facades\Alert;

class JoinController extends Controller
{
    public function submitQuestionResponse($code, $question_id, Request $request)
    {
        $type = $request->input("type");
        $label = $request->input("label");//Tiêu đề đáp án hoặc câu trả lời cho câu tự luận
        $option_key = $request->input("option_key");//Key của đáp án
        if (!in_array($type, ["multiple-choice", "single-choice", "rating", "open-survey"])) {
            return response()->json([
                'error' => true,
                'message' => "Câu hỏi không đúng định dạng!"
            ]);
        }
        if ($type!= "open-survey" && empty($option_key)){
            return response()->json([
                'error' => true,
                'message' => "Vui lòng chọn ít nhất 1 đáp án!"
            ]);
        }
        $webinar = Webinar::where('join_code', $code)->first();
        if (!$webinar) {
            return response()->json([
                'error' => true,
                'message' => "Buổi livestream không tồn tại!"
            ]);
        }
        $livestreams = $webinar->livestreams;
        $is_live_learning = $livestreams["is_live_learning"] ?? 0;
        if (!$is_live_learning) {

            return response()->json([
                'error' => true,
                'message' => "Buổi livestream đã kết thúc!"
            ]);
        }
        $questions = $livestreams["questions"] ?? [];
        if (!$questions) {
            return response()->json([
                'error' => true,
                'message' => "Đã hết hạn trả lời câu hỏi!"
            ]);
        }

        if ($questions["id"] != $question_id) {
            return response()->json([
                'error' => true,
                'message' => "Đã hết hạn trả lời câu hỏi!"
            ]);
        }


        $options = $questions["options"] ?? [];
        $valid_option_keys = array_column($options, 'value');
        if ($type != "open-survey") {
            if (is_array($option_key)) {
                $option_key = array_column($option_key, "value");
                if (array_diff($option_key, $valid_option_keys)) {
                    return response()->json([
                        'error' => true,
                        'message' => "Đáp án bạn chọn có đấp án không hợp lệ!"
                    ]);
                }
            } else {
                if (!in_array($option_key, $valid_option_keys)) {
                    return response()->json([
                        'error' => true,
                        'message' => "Đáp án bạn chọn không hợp lệ!"
                    ]);
                }
            }

        }

        $participantId = session('participant_id');
        if (!$participantId) {
            return response()->json([
                'error' => true,
                'message' => "Bạn cần đăng nhập để có thể trả lời câu hỏi!"
            ]);
        }
        $participant = WebinarParticipant::find($participantId);

        if (!$participant) {
            return response()->json([
                'error' => true,
                'message' => "Không tìm thấy thông tin người tham gia."
            ]);
        }

        $fileName = "question_webinar_" . $webinar->id . "_date_" . Carbon::now()->format("d_m_Y") . ".txt";
        $data = [];
        if (Storage::disk("public")->exists($fileName)) {
            $contentFile = Storage::disk("public")->get($fileName);
            $data = json_decode($contentFile, true);
        }
        $question_id_key = "question_" . $question_id;
        if (!empty($options)) {

//        Là câu trắc nghiệm [questions => [question_id=>[ $option_key=>[$participantId, ... ], ...]]
            if (is_array($option_key)) {
                foreach ($option_key as $key_answer) {
                    $user_ids = $data["questions"][$question_id_key][$key_answer] ?? [];
                    if (!in_array($participantId, $user_ids)) {
                        $data["questions"][$question_id_key][$key_answer][] = $participantId;
                    }
                }
            } else {
                $user_ids = $data["questions"][$question_id_key][$option_key] ?? [];
                if (!in_array($participantId, $user_ids)) {
                    $data["questions"][$question_id_key][$option_key][] = $participantId;
                }

                // XÓA user_id khỏi các đáp án khác
                foreach ($data["questions"][$question_id_key] ?? [] as $key => $ids) {
                    if ($key !== $option_key) {
                        $filtered = array_values(array_filter($ids, function ($id) use ($participantId) {
                            return (string)$id !== (string)$participantId;
                        }));
                        $data["questions"][$question_id_key][$key] = $filtered;
                    }
                }
            }

        } else {
//            Là câu tự luận [questions=>[question_id=>[ user_id => value, ...],...]]
            $data["questions"][$question_id_key][$participantId] = $label;
        }
        Storage::disk("public")->put($fileName, json_encode($data));

        return response()->json([
            'error' => false,
            'message' => "Gửi câu trả lời thành công!"
        ]);
    }

    /**
     * Show the join page for a webinar.
     * @param $code
     * @return \Illuminate\Container\Container|mixed|object
     */
    public function showJoinPage($code)
    {
        // Clear any problematic session state if we're forcing the join page
        if (request()->has('force_join')) {
            session()->forget('participant_id');
        }

        $webinar = Webinar::where('join_code', $code)->firstOrFail();
        $is_live_stream = $webinar->livestreams["is_live"] ?? 0;
        $is_live_learning = $webinar->livestreams["is_live_learning"] ?? 0;
        $now = Carbon::now();
        $nextSchedule = null;
        $currentSchedule = null;
        $inWaitingPeriod = false;
        $waitingMinutes = $webinar->waiting_time ?? 30;
        $webinarActive = false;
        $isLive = false;
        $waitingTimeRemaining = 0;
        $formattedStartTime = null;
        $formattedEndTime = null;

        $startTimeWebinar = null;
        if ($webinar->schedules) {
            foreach ($webinar->schedules as $schedule) {
                $scheduledTime = Carbon::parse($schedule['date'] . ' ' . $schedule['time']);
                if (Carbon::now() < $scheduledTime) {
                    $startTimeWebinar = $scheduledTime;
                    break;
                }
            }
        }
        if ($webinar->schedules) {
            foreach ($webinar->schedules as $schedule) {
                // Phân tích thời gian từ lịch
                $scheduledTime = Carbon::parse($schedule['date'] . ' ' . $schedule['time']);
                $scheduledEndTime = $scheduledTime->copy()->addMinutes($webinar->video_duration_minutes ?? 120);
                $waitingStartTime = $scheduledTime->copy()->subMinutes($waitingMinutes);

                // Luồng đơn giản: Kiểm tra xem webinar đang diễn ra
                if ($now->between($scheduledTime, $scheduledEndTime)) {
                    $currentSchedule = $schedule;
                    $webinarActive = true;
                    $isLive = true;
                    $formattedStartTime = $scheduledTime->format('H:i');
                    $formattedEndTime = $scheduledEndTime->format('H:i');
                    break;
                }

                // Kiểm tra xem đang trong thời gian chờ
                if ($now->between($waitingStartTime, $scheduledTime)) {
                    $currentSchedule = $schedule;
                    $inWaitingPeriod = true;

                    // Tính toán thời gian còn lại chi tiết hơn
                    $waitingTimeRemaining = $scheduledTime->diffInSeconds($now);
                    $formattedStartTime = $scheduledTime->format('H:i');
                    break;
                }

                // Nếu chưa tới thời gian, lưu lại lịch tiếp theo gần nhất
                if (
                    $scheduledTime->isAfter($now) &&
                    (!$nextSchedule || Carbon::parse($nextSchedule['date'] . ' ' . $nextSchedule['time'])->isAfter($scheduledTime))
                ) {
                    $nextSchedule = $schedule;
                }
            }
        }
        if ($is_live_stream || $is_live_learning) {
            // Kiểm tra người dùng đã đăng ký và có session hợp lệ
            // Chỉ làm điều này nếu người dùng không cố gắng truy cập trực tiếp trang tham gia
            if (!request()->has('force_join')) {
                $participantId = session('participant_id');
                if ($participantId) {
                    $participant = WebinarParticipant::find($participantId);
                    if ($participant && $participant->webinar_id === $webinar->id) {
                        return redirect()->route('join.enter', [
                            'code' => $code
                        ]);
                    }
                }
            }

            // Đăng ký trực tiếp nếu được yêu cầu
            if (request()->has('direct_register')) {
                return redirect()->route('join.show-form', $code);
            }
            return view('join', compact(
                'webinar',
                'nextSchedule',
                'is_live_stream',
                'is_live_learning',
                'currentSchedule',
                'inWaitingPeriod',
                'webinarActive',
                'waitingTimeRemaining',
                'formattedStartTime',
                'formattedEndTime',
                'startTimeWebinar',
                'isLive',
                'showRecording'
            ));
        }
        // Kiểm tra webinar đã kết thúc
        $webinarEnded = false;
        if (!$webinarActive && !$inWaitingPeriod && $webinar->schedules && count($webinar->schedules) > 0) {
            // Kiểm tra xem tất cả lịch đã kết thúc chưa
            $allSchedulesEnded = true;
            foreach ($webinar->schedules as $schedule) {
                $scheduledTime = Carbon::parse($schedule['date'] . ' ' . $schedule['time']);
                $scheduledEndTime = $scheduledTime->copy()->addMinutes($webinar->video_duration_minutes ?? 120);

                if ($scheduledEndTime->isFuture()) {
                    $allSchedulesEnded = false;
                    break;
                }
            }

            if ($allSchedulesEnded) {
                $webinarEnded = true;
            }
        }

        // Nếu webinar đã kết thúc và không cho phép xem lại, hiển thị thông báo
        if ($webinarEnded && !$webinar->allow_replay) {
            Alert::info('Thông báo', 'Buổi webinar đã kết thúc và không được phép xem lại.');
            return view('join', compact(
                'webinar',
                'webinarEnded'
            ));
        }

        // Kiểm tra webinar có video đã ghi sẵn
        $showRecording = $webinar->video_path ? true : false;

        // Kiểm tra người dùng đã đăng ký và có session hợp lệ
        // Chỉ làm điều này nếu người dùng không cố gắng truy cập trực tiếp trang tham gia
        if (!request()->has('force_join')) {
            $participantId = session('participant_id');
            if ($participantId) {
                $participant = WebinarParticipant::find($participantId);
                if ($participant && $participant->webinar_id === $webinar->id) {
                    return redirect()->route('join.enter', [
                        'code' => $code
                    ]);
                }
            }
        }

        // Đăng ký trực tiếp nếu được yêu cầu
        if (request()->has('direct_register')) {
            return redirect()->route('join.show-form', $code);
        }

        return view('join', compact(
            'webinar',
            'nextSchedule',
            'is_live_stream',
            'is_live_learning',
            'currentSchedule',
            'inWaitingPeriod',
            'webinarActive',
            'showRecording',
            'waitingTimeRemaining',
            'formattedStartTime',
            'formattedEndTime',
            'startTimeWebinar',
            'webinarEnded',
            'isLive'
        ));
    }

    public function loginLearning($code, Request $request)
    {
        $webinar = Webinar::where('join_code', $code)->firstOrFail();
        $is_live_stream = $webinar->livestreams["is_live_learning"] ?? 0;
        if (!$is_live_stream) {
            Alert::warning('Thông báo', 'Buổi livestream đã kết thúc!');
            return redirect()->back();
        }
        $student = WebinarParticipant::where("email", $request->input("email"))->where("is_student", 1)->first();

        if (!is_object($student)) {
            Alert::warning('Thông báo', 'Không tìm thấy tài khoản!');
            return redirect()->back();
        }
        if (!Hash::check($request->input("password"), $student->password)) {
            Alert::warning('Thông báo', 'Sai mật khẩu!');
            return redirect()->back();
        }
        cookie()->queue(cookie()->forget('webinar_participant_id'));
        $request->session()->put('participant_id', $student->id);

        return redirect()->route('join.enter', $webinar->join_code);
    }

    public
    function getLive($code)
    {
        if (Cache::has("livestream_ads")) {
            return response()->json([
                "error" => false,
                "mess" => "Thành công!",
                "data" => Cache::get("livestream_ads")
            ]);
        }

        $webinar = Webinar::where('join_code', $code)->first();
        if (!is_object($webinar)) {
            return response()->json([
                "error" => true,
                "mess" => "Phiên Live không tồn tại!",
                "data" => []
            ]);
        }
        Cache::put("livestream_ads", $webinar->livestreams);
        return response()->json([
            "error" => false,
            "mess" => "Thành công!",
            "data" => $webinar->livestreams
        ]);
    }

    private
    function check_recaptcha($GRecaptchaResponse = "")
    {
        if (isset($GRecaptchaResponse)) {
            $url = 'https://www.google.com/recaptcha/api/siteverify';
            $secret = setting('recaptcha_secret_key');
            $data = array(
                'secret' => $secret,
                'response' => $GRecaptchaResponse
            );
            $query = http_build_query($data);
            $options = array(
                'http' => array(
                    'header' => "Content-Type: application/x-www-form-urlencoded\r\n" .
                        "Content-Length: " . strlen($query) . "\r\n" .
                        "User-Agent:MyAgent/1.0\r\n",
                    'method' => 'POST',
                    'content' => $query
                )
            );
            $context = stream_context_create($options);
            $verify = file_get_contents($url, false, $context);
            $captcha_success = json_decode($verify);
            if ($captcha_success->success == false) {
                return false;
            } else if ($captcha_success->success == true) {
                return true;
            }
        } else {
            return false;
        }
    }

    /**
     * Process the join form and register participant.
     * @param Request $request
     * @param $code
     * @return \Illuminate\Container\Container|mixed|object
     */
    public
    function processJoinForm(Request $request, $code)
    {
        if (setting("recapcha_enable") === "1" && $this->check_recaptcha($request->input("g-recaptcha-response"))) {

            Alert::error('Thông báo', 'Xác minh Recaptcha không thành công!');
            return redirect()->back();
        }

        $webinar = Webinar::where('join_code', $code)->firstOrFail();

        $is_live_stream = $webinar->livestreams["is_live"] ?? 0;
        if (!$is_live_stream) {
            // Kiểm tra webinar đã kết thúc
            $webinarEnded = false;
            $now = Carbon::now();

            if ($webinar->schedules && count($webinar->schedules) > 0) {
                // Kiểm tra xem tất cả lịch đã kết thúc chưa
                $allSchedulesEnded = true;
                foreach ($webinar->schedules as $schedule) {
                    $scheduledTime = Carbon::parse($schedule['date'] . ' ' . $schedule['time']);
                    $scheduledEndTime = $scheduledTime->copy()->addMinutes($webinar->video_duration_minutes ?? 120);

                    if ($scheduledEndTime->isFuture()) {
                        $allSchedulesEnded = false;
                        break;
                    }
                }

                if ($allSchedulesEnded) {
                    $webinarEnded = true;
                }
            }

            // Nếu webinar đã kết thúc và không cho phép xem lại, hiển thị thông báo và quay lại
            if ($webinarEnded && !$webinar->allow_replay) {
                Alert::info('Thông báo', 'Buổi webinar đã kết thúc và không được phép xem lại.');
                return redirect()->route('join.show', ['code' => $code]);
            }
        }

        // Clear any potentially problematic session data
        $request->session()->forget('participant_id');


        // Get the join settings
        $settings = $webinar->join_settings ?? [];

        // Validate the request
        $rules = [];

        if (isset($settings['name_required']) && $settings['name_required']) {
            $rules['name'] = 'required|string|max:255';
        } else {
            $rules['name'] = 'nullable|string|max:255';
        }

        $pattern = '/^0\d{9,10}$/'; // Pattern mới: bắt đầu bằng 0 và có 10-11 chữ số

        if (isset($settings['phone_required']) && $settings['phone_required']) {
            $rules['phone'] = ['required', 'string', 'max:20', "regex:$pattern"];
        } else {
            $rules['phone'] = ['nullable', 'string', 'max:20', "regex:$pattern"];
        }

        if (isset($settings['email_required']) && $settings['email_required']) {
            $rules['email'] = 'required|email|max:255';
        } else {
            $rules['email'] = 'nullable|email|max:255';
        }

        try {
            $messages = [
                // Messages cho trường name
                'name.required' => 'Tên là bắt buộc.',
                'name.string' => 'Tên phải là chuỗi ký tự.',
                'name.max' => 'Tên không được vượt quá 255 ký tự.',

                // Messages cho trường phone
                'phone.required' => 'Số điện thoại là bắt buộc.',
                'phone.string' => 'Số điện thoại phải là chuỗi ký tự.',
                'phone.max' => 'Số điện thoại không được vượt quá 20 ký tự.',
                'phone.regex' => 'Số điện thoại không đúng định dạng.',

                // Messages cho trường email
                'email.required' => 'Email là bắt buộc.',
                'email.email' => 'Email phải đúng định dạng (ví dụ: <EMAIL>).',
                'email.max' => 'Email không được vượt quá 255 ký tự.',
            ];
            $validatedData = $request->validate($rules, $messages);

            // Kiểm tra xem số điện thoại đã tồn tại chưa
            $phone = $validatedData['phone'] ?? null;
            $existingParticipant = null;

            if ($phone) {
                $existingParticipant = WebinarParticipant::where('webinar_id', $webinar->id)
                    ->where('phone', $phone)
                    ->first();
            }

            if ($existingParticipant) {
                // Cập nhật thông tin và tăng số lần tham gia
                $existingParticipant->update([
                    'name' => $validatedData['name'] ?? $existingParticipant->name,
                    'email' => $validatedData['email'] ?? $existingParticipant->email,
                    'joined_at' => Carbon::now(),
                    'join_count' => $existingParticipant->join_count + 1,
                ]);

                $participant = $existingParticipant;
            } else {
                // Tạo người tham gia mới
                $participant = WebinarParticipant::create([
                    'webinar_id' => $webinar->id,
                    'name' => $validatedData['name'] ?? 'Khách',
                    'phone' => $validatedData['phone'] ?? null,
                    'email' => $validatedData['email'] ?? null,
                    'ip_address' => $request->ip(),
                    'joined_at' => Carbon::now(),
                    'join_count' => 0,
                    'utm_source' => $request->input('utm_source'),
                    'utm_medium' => $request->input('utm_medium'),
                    'utm_campaign' => $request->input('utm_campaign'),
                    'utm_term' => $request->input('utm_term'),
                    'utm_content' => $request->input('utm_content'),
                ]);

                // Phát event có người tham gia mới

            }
            try {
                event(new WebinarParticipantRegistered($participant, $webinar));

                // Trigger email marketing campaigns for new participants only
                if (!$existingParticipant) {
                    event(new WebinarParticipantRegisteredForEmail($participant, $webinar));
                }
            } catch (\Exception $e) {
                \Log::error('Event error: ' . $e->getMessage());
            }
            // Lưu ID người tham gia vào session
            $request->session()->put('participant_id', $participant->id);

            // Also store in a cookie as fallback
            cookie()->queue('webinar_participant_id', $participant->id, 60 * 24 * 365); // 24 hours

            Alert::success('Thành công', 'Đăng ký tham gia thành công!');

            // Tạo URL để chuyển hướng - use absolute URL
            $targetUrl = url('/join/' . $code . '/enter');

            // Chuyển hướng sử dụng URL tuyệt đối
            return redirect($targetUrl);
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->withErrors(['error' => 'Có lỗi xảy ra khi xử lý form: ' . $e->getMessage()]);
        }
    }


    /**
     * Enter webinar
     *
     * @param Request $request
     * @param string $code
     * @return \Illuminate\Contracts\View\View
     */
    public
    function enterWebinar(Request $request, $code)
    {
        $webinar = Webinar::where('join_code', $code)->firstOrFail();
        // Get participant from session - do not use request query param to avoid link sharing
        $participantId = $request->session()->get('participant_id');

        if ($participantId) {
            $participantId = (int)$participantId; // Ensure integer type
        }


        // Check cookie as fallback
        if (!$participantId) {
            $participantId = $request->cookie('webinar_participant_id');

            // If found in cookie but not in session, restore to session
            if ($participantId) {
                $request->session()->put('participant_id', $participantId);
            }
        }
        // Prevent direct access without registration - redirect to join page
        if (!$participantId) {
            return redirect()->route('join.show', ['code' => $code, 'force_join' => 'true']);
        }

        // Find the actual participant in the database
        $participant = WebinarParticipant::find($participantId);

        // Remove the automatic creation of demo participant to prevent bypassing the registration
        // If participant not found or doesn't belong to this webinar, redirect to join page
        if (!$participant || $participant->webinar_id !== $webinar->id) {
            // Clear invalid session and redirect

            $request->session()->forget('participant_id');
            cookie()->queue(cookie()->forget('webinar_participant_id'));
            return redirect()->route('join.show', ['code' => $code, 'force_join' => 'true']);
        }

        // Kiểm tra trạng thái webinar
        $now = Carbon::now();
        $isLive = false;
        $currentSchedule = null;
        $hasAccessibleContent = false;
        $inWaitingPeriod = false;
        $waitingTimeRemaining = 0;
        $waitingMinutes = $webinar->waiting_time ?? 30;
        $formattedStartTime = null;
        $formattedEndTime = null;
        $videoTimestamp = 0; // Default starting point

        $startTimeWebinar = null;
        if ($webinar->schedules) {
            foreach ($webinar->schedules as $schedule) {
                $scheduledTime = Carbon::parse($schedule['date'] . ' ' . $schedule['time']);
                if (Carbon::now() < $scheduledTime) {
                    $startTimeWebinar = $scheduledTime;
                    break;
                }
            }
            foreach ($webinar->schedules as $schedule) {
                $scheduledTime = Carbon::parse($schedule['date'] . ' ' . $schedule['time']);
                $scheduledEndTime = $scheduledTime->copy()->addMinutes($webinar->video_duration_minutes ?? 120);
                $waitingStartTime = $scheduledTime->copy()->subMinutes($waitingMinutes);

                // Đơn giản: kiểm tra webinar đang diễn ra
                if ($now->between($scheduledTime, $scheduledEndTime)) {
                    $isLive = true;
                    $inWaitingPeriod = false; // Ensure waiting period is false when live
                    $currentSchedule = $schedule;
                    $hasAccessibleContent = true;
                    $formattedStartTime = $scheduledTime->format('H:i');
                    $formattedEndTime = $scheduledEndTime->format('H:i');

                    // Calculate how many seconds into the webinar we are
                    // This creates the illusion of a live stream - viewers who join midway will see the video from that point
                    $videoTimestamp = max(0, $scheduledTime->diffInSeconds($now, false));
                    $webinar->start_date = $scheduledTime;
                    $webinar->end_date = $scheduledEndTime;

                    \Log::info('Webinar is live, timestamp calculation: ' . json_encode([
                            'now' => $now->toDateTimeString(),
                            'scheduled_time' => $scheduledTime->toDateTimeString(),
                            'diff_in_seconds' => $videoTimestamp,
                        ]));

                    break;
                }

                // Kiểm tra đang trong thời gian chờ
                if ($now->between($waitingStartTime, $scheduledTime)) {
                    $currentSchedule = $schedule;
                    $inWaitingPeriod = true;
                    $waitingTimeRemaining = $now->diffInSeconds($scheduledTime);
                    $hasAccessibleContent = true; // Cho phép truy cập trong phòng chờ
                    $formattedStartTime = $scheduledTime->format('H:i');
                    $webinar->start_date = $scheduledTime;
                    $webinar->end_date = $scheduledEndTime;
                    break;
                }
            }
        }

        $is_live_stream = $webinar->livestreams["is_live"] ?? 0;
        $is_live_learning = $webinar->livestreams["is_live_learning"] ?? 0;

        $type_live = $webinar->livestreams["type"] ?? "";
        $link_live = $webinar->livestreams["link"] ?? "";

        // Kiểm tra webinar đã kết thúc để hiển thị giao diện phù hợp
        $webinarEnded = false;

        if (!$is_live_learning) {
            if (!$is_live_stream) {
                if (!$isLive && !$inWaitingPeriod && $webinar->schedules && count($webinar->schedules) > 0) {
                    // Kiểm tra xem tất cả lịch đã kết thúc chưa
                    $allSchedulesEnded = true;
                    foreach ($webinar->schedules as $schedule) {
                        $scheduledTime = Carbon::parse($schedule['date'] . ' ' . $schedule['time']);
                        $scheduledEndTime = $scheduledTime->copy()->addMinutes($webinar->video_duration_minutes ?? 120);

                        if ($scheduledEndTime->isFuture()) {
                            $allSchedulesEnded = false;
                            break;
                        }
                    }

                    if ($allSchedulesEnded) {
                        $webinarEnded = true;
                    }
                }

                // If there's a video available, check if replay is allowed when webinar has ended
                if ($webinar->video_path) {
                    if ($webinarEnded && !$webinar->allow_replay) {
                        Alert::info('Thông báo', 'Buổi webinar đã kết thúc và không được phép xem lại.');
                        return redirect()->route('join.show', ['code' => $code, 'force_join' => 'true']);
                    }

                    $hasAccessibleContent = true;
                    // If we're showing recorded content, don't show waiting period
                    if ($isLive) {
                        $inWaitingPeriod = false;
                    }
                }

                // Nếu không có nội dung gì để xem, chuyển hướng về trang chủ
                if (!$hasAccessibleContent) {
                    Alert::info('Thông báo', 'Webinar chưa bắt đầu hoặc đã kết thúc.');
                    return redirect()->route('join.show', ['code' => $code, 'force_join' => 'true']);
                }

            }
        }

        // Cập nhật số lần tham gia và thời gian tham gia
        $participant->update([
//            'join_count' => $participant->join_count + 1,
            'joined_at' => now(),
            'device_type' => $this->detectDeviceType($request) // Tự động phát hiện thiết bị
        ]);

        // Lấy tất cả bình luận
        $comments = $webinar->comments()
            ->with('participant:id,name,email')
            ->orderBy('video_timestamp', 'asc')
            ->get()
            ->map(function ($comment) {
                return $this->itemCollectComments($comment);
            });
        // Kết hợp với bình luận seeding
        $seededComments = is_array($webinar->seeded_comments) ? $webinar->seeded_comments : [];
        $advertisement_slots = $webinar->advertisement_slots;
        $advertisements = [];
        if (is_array($advertisement_slots) && !empty($advertisement_slots)) {
            foreach ($advertisement_slots as $ads) {
                $advertisement = Advertisement::find($ads["advertisement_id"]);

                $product = $advertisement->product;
                $price = (int)$advertisement->sale_price;
                $oldPrice = (int)$advertisement->original_price;

                $discount = $oldPrice > 0 ? ($oldPrice - $price) * 100 / $oldPrice : 0;
                $advertisements[] = [
                    "id" => $advertisement->id,
                    "name" => $advertisement->name,
                    "type" => $advertisement->type,
                    "order_method" => $advertisement->order_method,
                    "at" => $ads["time"], // Show at 5 seconds into the video
                    "showIn" => $advertisement->display_time, // Stays open for 10 seconds
                    "image" => $advertisement->image ? Storage::disk("public")->url($advertisement->image) : null,
                    "image_url" => $advertisement->url,
                    "redirect_url_ads" => $advertisement->redirect_url,
                    "price" => number_format($price) . " ₫",
                    "oldPrice" => number_format($oldPrice) . " ₫",
                    "detail" => [
                        "id" => @$product->id,
                        "title" => @$product->name, // More specific title
                        "image" => is_object($product) && !empty($product->image) ? Storage::disk("public")->url($product->image) : null,
                        "tags" => @$product->tag,
                        "description" => null,
                        "price" => number_format($price) . " ₫",
                        "oldPrice" => number_format($oldPrice) . " ₫",
                        "discount" => ceil($discount) . "%",
                        "quantity" => @$product->stock_quantity,
                        "totalSold" => $advertisement->virtual_quantity,
                    ],
                    "form_required" => [
                        "name" => $advertisement->require_name,
                        "email" => $advertisement->require_email,
                        "phone" => $advertisement->require_phone,
                    ]
                ];
            }
        }
        // Mở trang xem webinar
        return view('join.webinar', compact(
            'webinar',
            'is_live_stream',
            'is_live_learning',
            'type_live',
            'link_live',
            "advertisements",
            'participantId',
            'participant',
            'comments',
            'startTimeWebinar',
            'seededComments',
            'isLive',
            'inWaitingPeriod',
            'waitingTimeRemaining',
            'currentSchedule',
            'webinarEnded',
            'formattedStartTime',
            'formattedEndTime',
            'videoTimestamp'
        ));
    }

    /**
     * Submit a comment for the webinar.
     * @param Request $request
     * @param $code
     * @return mixed
     */
    public
    function submitComment(Request $request, $code)
    {
        $webinar = Webinar::where('join_code', $code)->firstOrFail();
        $participantId = session('participant_id');

        if (!$participantId) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn cần đăng nhập để bình luận.'
            ], 403);
        }

        $participant = WebinarParticipant::find($participantId);

        if (!$participant) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy thông tin người tham gia.'
            ], 404);
        }

        // Validate content
        $validatedData = $request->validate([
            'content' => 'required|string|max:500',
            'video_timestamp' => 'nullable|integer'
        ]);

        // Create comment
        $comment = $webinar->comments()->create([
            'content' => $request->input('content'),
            'name' => $participant->name,
            'participant_id' => $participant->id,
            'hidden' => false,
            'video_timestamp' => $request->input('video_timestamp'),
        ]);

        // Get duration parameter if provided
        $duration = $request->get('video_timestamp');

        // Cache key specific to this webinar and duration
//        $cacheKey = 'comments_' . $webinar->id . '_' . ($duration ? $duration : 'all');
        $cacheKey = 'comments_' . $webinar->id;

        if (Cache::has($cacheKey)) {
            $messages = Cache::get($cacheKey);
            $messages = $this->addToCollectComments($messages, $comment, $participant->id, $participant->name);
        } else {
            $messages = $this->getCommentRealtime($webinar, $duration);
        }


        Cache::put($cacheKey, $messages->sortBy('video_timestamp'), 60);


        // Phát event có bình luận mới
        event(new WebinarCommentSubmitted($comment, $webinar));


        return response()->json([
            'success' => true,
            'comment' => [
                'id' => $comment->id,
                'content' => $comment->content,
                'name' => $participant->name,
                'created_at' => $comment->created_at->format('d/m/Y H:i:s'),
                'video_timestamp' => $comment->video_timestamp,
            ]
        ]);

    }

    /**
     * Get recent comments for the webinar.
     */
    public
    function getComments($code)
    {
        $webinar = Webinar::where('join_code', $code)->first();

        // Get duration parameter if provided
        $duration = request()->query('duration');


        // Cache key specific to this webinar and duration
//        $cacheKey = 'comments_' . $webinar->id . '_' . ($duration ? $duration : 'all');
        $cacheKey = 'comments_' . $webinar->id;

        $comments_fake = $webinar->seeded_comments;

        $messages = $this->mergeCommentFakeAndAuth($duration, $comments_fake, $webinar, $cacheKey);


//        Tính số lượt view livestream
        $userId = session('participant_id');
        $viewerKey = "live_viewers:$webinar->id";
        $viewsKey = "live_unique_views:$webinar->id";

        // === 1. Cập nhật người đang xem (file cache) ===
        $viewers = Cache::get($viewerKey, []);
        $viewers[$userId] = now()->timestamp;

        // Loại bỏ user không ping > 35s
        $viewers = collect($viewers)->filter(function ($timestamp) {
            return now()->timestamp - $timestamp <= 35;
        })->toArray();

        Cache::put($viewerKey, $viewers, now()->addMinutes(5));

        // === 2. Ghi nhận lượt xem (duy nhất) ===
        $views = Cache::get($viewsKey, []);

        if (!in_array($userId, $views)) {
            $views[] = $userId;
            Cache::put($viewsKey, $views, now()->addHours(6)); // giữ 1 ngày
        }

        return response()->json($messages);
    }

    private
    function addToCollectComments($messages, $comment, $participant_id = null, $participant_name = null)
    {
        return $messages->add($this->itemCollectComments($comment));
    }

    private
    function mergeCommentFakeAndAuth($duration, $comments_fake, $webinar, $cacheKey)
    {
        if (Cache::has($cacheKey)) {
            $messageData = Cache::get($cacheKey);

        } else {
            $messageData = $this->getCommentRealtime($webinar, $duration);
        }
        $putCache = false;
        if (is_array($comments_fake) && !empty($comments_fake)) {

            foreach ($comments_fake as $fake) {
                if (is_array($fake)) {
                    $timeString = $fake["time"];
                    list($hours, $minutes, $seconds) = explode(':', $timeString);
                    $times_show_fake = ((int)$hours * 3600) + ((int)$minutes * 60) + (int)$seconds;
                    if ($duration < $times_show_fake) {
                        break;
                    }
                    //                            Nếu comment fake chưa được add
                    $fakeFind = $webinar->comments()
                        ->where("content", $fake['content'])
                        ->where("participant_id", null)
                        ->where("video_timestamp", $times_show_fake)
                        ->where("name", $fake["name"])
                        ->first();
                    if (!is_object($fakeFind)) {
                        $created_at_comment = Carbon::now();
                        if (is_array($webinar->schedules)) {
                            foreach ($webinar->schedules as $schedules) {
                                $date = $schedules["date"];
                                $timeStart = $schedules["time"];
                                if (Carbon::now()->format("Y-m-d") == $date) {
                                    $created_at_comment = Carbon::createFromFormat("Y-m-d H:i", $date . " " . $timeStart)->addSeconds($times_show_fake);
                                }
                            }
                        }
                        $comment = WebinarComment::create([
                            'webinar_id' => $webinar->id,
                            'content' => $fake['content'],
                            'name' => $fake["name"],
                            'participant_id' => null,
                            'hidden' => false,
                            'created_at' => $created_at_comment,
                            'updated_at' => $created_at_comment,
                            'video_timestamp' => $times_show_fake,
                        ]);

                        $messageData = $this->addToCollectComments($messageData, $comment, null, $fake["name"]);
                        $putCache = true;
                    } else {
                        //                      Nếu đã add comment nhưng chưa được hiển thị
                        if ($fakeFind->hidden) {
                            $fakeFind->update(["hidden" => false]);
                            $messageData = $this->addToCollectComments($messageData, $fakeFind, null, $fake["name"]);
                            $putCache = true;
                        }
                    }

                }
            }
        }
        $messageData = $messageData->sortBy('video_timestamp');
        if ($putCache) {
            Cache::put($cacheKey, $messageData->unique('id'), 60);
        }
        return $messageData;
    }

    private
    function itemCollectComments($comment)
    {
        return [
            'id' => $comment->id,
            'content' => $comment->content,
            'participant' => [
                'id' => $comment->participant_id,
                'name' => $comment->name,
            ],
            'created_at' => $comment->created_at->format('H:i'),
            'video_timestamp' => $comment->video_timestamp,
        ];
    }

    private
    function getCommentRealtime($webinar, $duration)
    {
        $query = $webinar->comments()->with('participant')
            ->orderBy('video_timestamp', 'asc');
        if ($duration && is_numeric($duration)) {
            $duration = (int)$duration;
            // Get comments that have a timestamp less than or equal to the provided duration
            $query->where(function ($q) use ($duration) {
                $q->whereNull('video_timestamp')
                    ->orWhere('video_timestamp', '<=', $duration);
            });
        }

        return $query->limit(100)
            ->get()
            ->map(function ($comment) use ($webinar) {
                if ($webinar->only_show_my_comment) {
                    if ($comment->participant_id == session('participant_id')) {
                        return [
                            'id' => $comment->id,
                            'content' => $comment->content,
                            'participant' => [
                                'id' => @$comment->participant->id,
                                'name' => @$comment->participant->name,
                            ],
                            'created_at' => $comment->created_at->format('H:i'),
                            'video_timestamp' => $comment->video_timestamp,
                        ];
                    }
                } else {
                    return [
                        'id' => $comment->id,
                        'content' => $comment->content,
                        'participant' => [
                            'id' => @$comment->participant->id,
                            'name' => @$comment->participant->name,
                        ],
                        'created_at' => $comment->created_at->format('H:i'),
                        'video_timestamp' => $comment->video_timestamp,
                    ];
                }
            });
    }

    /**
     * Cập nhật thời gian xem cho người tham gia
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public
    function updateViewDuration(Request $request, $code)
    {
        // Tìm webinar từ mã tham gia
        $webinar = Webinar::where('join_code', $code)->firstOrFail();

        // Kiểm tra xem yêu cầu có thông tin người tham gia không
        if (!$request->has('participant_id') || !$request->has('duration')) {
            return response()->json(['error' => 'Thiếu thông tin người tham gia hoặc thời gian xem'], 400);
        }

        // Tìm người tham gia
        $participant = WebinarParticipant::find($request->participant_id);
        if (!$participant || $participant->webinar_id !== $webinar->id) {
            return response()->json(['error' => 'Không tìm thấy người tham gia'], 404);
        }

        // Lấy thông tin thiết bị (nếu có)
        $deviceType = $request->device_type ?? $this->detectDeviceType($request);

        // Cập nhật thông tin
        $participant->update([
            'view_duration' => $request->duration,
            'device_type' => $deviceType
        ]);

        return response()->json(['success' => true]);
    }

    /**
     * Phát hiện loại thiết bị từ user agent
     *
     * @param Request $request
     * @return string
     */
    private
    function detectDeviceType(Request $request)
    {
        $userAgent = $request->header('User-Agent');

        if (
            preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i', $userAgent) ||
            preg_match('/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i', substr($userAgent, 0, 4))
        ) {
            return 'mobile';
        }

        if (preg_match('/tablet|ipad|playbook|silk/i', $userAgent)) {
            return 'tablet';
        }

        return 'desktop';
    }

    /**
     * Show the registration form for a webinar.
     */
    public
    function showRegistrationForm($code)
    {

        $webinar = Webinar::where('join_code', $code)->firstOrFail();

        // Capture UTM parameters
        $utmParams = [
            'utm_source' => request()->input('utm_source'),
            'utm_medium' => request()->input('utm_medium'),
            'utm_campaign' => request()->input('utm_campaign'),
            'utm_term' => request()->input('utm_term'),
            'utm_content' => request()->input('utm_content'),
        ];

        return view('join-form', compact('webinar', 'utmParams'));
    }
}
