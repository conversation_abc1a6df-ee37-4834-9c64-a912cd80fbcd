<?php

namespace App\Jobs;

use App\Models\EmailLog;
use App\Models\EmailWorkflow;
use App\Models\WebinarParticipant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendEmailMarketingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $emailLog;

    /**
     * Create a new job instance.
     */
    public function __construct(EmailLog $emailLog)
    {
        $this->emailLog = $emailLog;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Get email log with relationships
            $emailLog = $this->emailLog->load(['workflow.emailTemplate', 'participant', 'campaign.webinar']);
            
            // Check if email should still be sent
            if ($emailLog->status !== 'pending') {
                Log::info("Email log {$emailLog->id} is not pending, skipping");
                return;
            }

            // Check if scheduled time has passed
            if ($emailLog->scheduled_at && $emailLog->scheduled_at->isFuture()) {
                Log::info("Email log {$emailLog->id} is scheduled for future, skipping");
                return;
            }

            // Prepare email data
            $template = $emailLog->workflow->emailTemplate;
            $participant = $emailLog->participant;
            $webinar = $emailLog->campaign->webinar;

            // Prepare variables for template
            $variables = $this->prepareEmailVariables($participant, $webinar);
            
            // Render email content
            $rendered = $template->renderContent($variables);

            // Send email using Laravel Mail
            Mail::send([], [], function ($message) use ($rendered, $emailLog) {
                $message->to($emailLog->recipient_email, $emailLog->recipient_name)
                        ->subject($rendered['subject'])
                        ->html($rendered['content']);
                
                // Set from address from settings
                $fromEmail = \App\Models\Setting::get('smtp_from_address', config('mail.from.address'));
                $fromName = \App\Models\Setting::get('smtp_from_name', config('mail.from.name'));
                $message->from($fromEmail, $fromName);
            });

            // Mark as sent
            $emailLog->markAsSent();
            
            Log::info("Email sent successfully to {$emailLog->recipient_email}");

        } catch (\Exception $e) {
            // Mark as failed
            $this->emailLog->markAsFailed($e->getMessage());
            
            Log::error("Failed to send email to {$this->emailLog->recipient_email}: " . $e->getMessage());
            
            // Re-throw exception to trigger job retry
            throw $e;
        }
    }

    /**
     * Prepare variables for email template.
     */
    private function prepareEmailVariables($participant, $webinar)
    {
        $variables = [
            '{{participant_name}}' => $participant->name ?? '',
            '{{participant_email}}' => $participant->email ?? '',
            '{{participant_phone}}' => $participant->phone ?? '',
        ];

        if ($webinar) {
            $variables['{{webinar_title}}'] = $webinar->title ?? '';
            $variables['{{webinar_speaker}}'] = $webinar->speaker ?? '';
            $variables['{{join_url}}'] = $webinar->join_url ?? '';
            
            // Add schedule-related variables
            if ($webinar->schedules && is_array($webinar->schedules)) {
                $nextSchedule = collect($webinar->schedules)
                    ->where('date', '>=', now()->format('Y-m-d'))
                    ->first();
                
                if ($nextSchedule) {
                    $variables['{{webinar_date}}'] = \Carbon\Carbon::parse($nextSchedule['date'] . ' ' . $nextSchedule['time'])->format('d/m/Y H:i');
                    $variables['{{time_until_webinar}}'] = \Carbon\Carbon::parse($nextSchedule['date'] . ' ' . $nextSchedule['time'])->diffForHumans();
                }
            }
        }

        // Add registration date
        if ($participant->created_at) {
            $variables['{{registration_date}}'] = $participant->created_at->format('d/m/Y H:i');
        }

        // Add tracking URLs (for future implementation)
        $variables['{{tracking_pixel}}'] = url("/email-tracking/open/{$this->emailLog->id}");
        $variables['{{unsubscribe_url}}'] = url("/email-marketing/unsubscribe/{$participant->id}");

        return $variables;
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        $this->emailLog->markAsFailed($exception->getMessage());
        Log::error("Email job failed permanently for log {$this->emailLog->id}: " . $exception->getMessage());
    }
}
