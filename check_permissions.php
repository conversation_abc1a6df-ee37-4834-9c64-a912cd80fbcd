<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

echo "=== Email Marketing Permission Check ===\n\n";

// Check if permission exists
$permission = Permission::where('name', 'email marketing')->first();
if ($permission) {
    echo "✅ Permission 'email marketing' exists (ID: {$permission->id})\n";
} else {
    echo "❌ Permission 'email marketing' does NOT exist\n";
}

// Check admin user
$adminUser = User::where('email', '<EMAIL>')->first();
if ($adminUser) {
    echo "\n--- Admin User Check ---\n";
    echo "User: {$adminUser->name} ({$adminUser->email})\n";
    echo "Roles: " . $adminUser->roles->pluck('name')->implode(', ') . "\n";
    echo "Has 'email marketing' permission: " . ($adminUser->can('email marketing') ? 'YES' : 'NO') . "\n";
    
    // List all permissions for this user
    echo "\nAll permissions for this user:\n";
    $permissions = $adminUser->getAllPermissions();
    foreach ($permissions as $perm) {
        if (strpos($perm->name, 'email') !== false || strpos($perm->name, 'marketing') !== false) {
            echo "  - {$perm->name}\n";
        }
    }
} else {
    echo "\n❌ Admin user not found\n";
}

// Check administrator role
$adminRole = Role::where('name', 'administrator')->first();
if ($adminRole) {
    echo "\n--- Administrator Role Check ---\n";
    echo "Role: {$adminRole->name}\n";
    echo "Has 'email marketing' permission: " . ($adminRole->hasPermissionTo('email marketing') ? 'YES' : 'NO') . "\n";
    
    // List email-related permissions
    echo "\nEmail-related permissions in administrator role:\n";
    $rolePermissions = $adminRole->permissions;
    foreach ($rolePermissions as $perm) {
        if (strpos($perm->name, 'email') !== false || strpos($perm->name, 'marketing') !== false) {
            echo "  - {$perm->name}\n";
        }
    }
}

echo "\n=== End Check ===\n";
